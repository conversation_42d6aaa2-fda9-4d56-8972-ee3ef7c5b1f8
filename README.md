# QuotaSnap - Sales Commission Tracking App

A comprehensive mobile application for tracking sales agents' commissions in real time across multiple products, clients, and tiers. Perfect for fintech, insurance, and real estate sectors.

## 🚀 Features

- **Real-time Commission Tracking**: Track deal closures and commissions instantly
- **Tiered Commission Rules**: Flexible commission structures per product
- **Performance Analytics**: Monthly summaries with interactive graphs
- **Leaderboard**: Competitive performance tracking
- **Export Capabilities**: Generate Excel/PDF reports
- **Multi-user Support**: Sales Agents, Managers, and Finance/Admin roles

## 🏗️ Architecture

### Tech Stack
- **Frontend**: React Native with Material UI components
- **Backend**: Spring Boot (Java) REST API
- **Database**: MySQL
- **Authentication**: JWT + OAuth2
- **Charts**: React Native Chart Kit
- **Export**: Custom PDF/Excel generation

### Project Structure
```
QuotaSnap/
├── backend/                 # Spring Boot API
│   ├── src/main/java/
│   ├── src/main/resources/
│   └── pom.xml
├── mobile/                  # React Native App
│   ├── src/
│   ├── android/
│   ├── ios/
│   └── package.json
├── database/               # MySQL schemas and migrations
├── docs/                   # Documentation
└── README.md
```

## 🎨 Design System

### Color Scheme
- **Primary**: Indigo (#3F51B5)
- **Secondary**: Neon Orange (#FF6D00)
- **Background**: White (#FFFFFF)
- **Text**: Charcoal (#424242)

### UI Components
- Material Design principles
- Consistent spacing and typography
- Responsive layouts for various screen sizes

## 🚀 Getting Started

### Prerequisites
- Node.js (v16+)
- Java 11+
- MySQL 8.0+
- Android Studio / Xcode for mobile development

### Backend Setup
```bash
cd backend
./mvnw spring-boot:run
```

### Mobile App Setup
```bash
cd mobile
npm install
npx react-native run-android  # or run-ios
```

### Database Setup
```bash
cd database
mysql -u root -p < schema.sql
mysql -u root -p < seed_data.sql
```

## 📱 User Roles & Permissions

### Sales Agents
- View personal commission data
- Input deal closures
- Access commission calculator
- View personal performance metrics

### Managers
- View team performance
- Access all agent data
- Manage commission rules
- Generate team reports

### Finance/Admin
- Full system access
- Manage users and permissions
- Configure commission structures
- Export financial reports

## 🔧 Development Guidelines

### Code Organization
- Follow clean architecture principles
- Use meaningful naming conventions
- Implement proper error handling
- Write comprehensive tests

### Git Workflow
- Feature branch workflow
- Conventional commit messages
- Code review requirements
- Automated testing on PR

## 📊 Key Screens

1. **Dashboard**: Overview of commissions, recent deals, performance metrics
2. **Commission Calculator**: Real-time commission calculation tool
3. **Leaderboard**: Agent performance rankings
4. **Deal Entry**: Form for logging new deals
5. **Reports**: Analytics and export functionality

## 🔐 Security

- JWT-based authentication
- OAuth2 integration for social login
- Role-based access control
- Data encryption at rest and in transit

## 📈 Performance

- Optimized database queries
- Efficient state management
- Lazy loading for large datasets
- Caching strategies for frequently accessed data

## 🧪 Testing

- Unit tests for business logic
- Integration tests for API endpoints
- E2E tests for critical user flows
- Performance testing for scalability

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📞 Support

For support and questions, please contact the development team or create an issue in the repository.
