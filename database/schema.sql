-- QuotaSnap Database Schema
-- MySQL 8.0+ compatible

-- Create database
CREATE DATABASE IF NOT EXISTS quotasnap_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE quotasnap_db;

-- Create users table
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    first_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255),
    phone_number VARCHAR(20),
    role ENUM('SALES_AGENT', 'MANAGER', 'FINANCE', 'ADMIN') NOT NULL,
    status ENUM('ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING', 'ARCHIVED') NOT NULL DEFAULT 'ACTIVE',
    employee_id VARCHAR(50) UNIQUE,
    department VARCHAR(50),
    hire_date DATETIME,
    last_login DATETIME,
    profile_image_url VARCHAR(500),
    provider VARCHAR(20),
    provider_id VARCHAR(100),
    email_verified <PERSON><PERSON><PERSON>EA<PERSON> DEFAULT FALSE,
    manager_id BIGINT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    version BIGINT DEFAULT 0,
    
    FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_email (email),
    INDEX idx_user_role (role),
    INDEX idx_user_status (status),
    INDEX idx_user_manager (manager_id),
    INDEX idx_user_employee_id (employee_id)
);

-- Create products table
CREATE TABLE products (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    product_code VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    category VARCHAR(50) NOT NULL,
    base_price DECIMAL(15,2) NOT NULL,
    status ENUM('ACTIVE', 'INACTIVE', 'DISCONTINUED', 'DRAFT') NOT NULL DEFAULT 'ACTIVE',
    is_commission_eligible BOOLEAN NOT NULL DEFAULT TRUE,
    min_deal_value DECIMAL(15,2),
    max_deal_value DECIMAL(15,2),
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    version BIGINT DEFAULT 0,
    
    INDEX idx_product_code (product_code),
    INDEX idx_product_category (category),
    INDEX idx_product_status (status)
);

-- Create commission_rules table
CREATE TABLE commission_rules (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    product_id BIGINT NOT NULL,
    commission_type ENUM('PERCENTAGE', 'FIXED', 'TIERED', 'HYBRID') NOT NULL,
    commission_rate DECIMAL(5,2) NOT NULL,
    fixed_amount DECIMAL(15,2),
    tier_level INT NOT NULL DEFAULT 1,
    min_deal_value DECIMAL(15,2),
    max_deal_value DECIMAL(15,2),
    min_quantity INT,
    max_quantity INT,
    effective_from DATETIME NOT NULL,
    effective_to DATETIME,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    description TEXT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    version BIGINT DEFAULT 0,
    
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    INDEX idx_commission_rule_product (product_id),
    INDEX idx_commission_rule_tier (tier_level),
    INDEX idx_commission_rule_active (is_active),
    INDEX idx_commission_rule_effective (effective_from, effective_to)
);

-- Create deals table
CREATE TABLE deals (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    deal_reference VARCHAR(100) NOT NULL UNIQUE,
    agent_id BIGINT NOT NULL,
    product_id BIGINT NOT NULL,
    client_name VARCHAR(200) NOT NULL,
    client_email VARCHAR(100),
    client_phone VARCHAR(20),
    deal_value DECIMAL(15,2) NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    deal_date DATETIME NOT NULL,
    close_date DATETIME,
    status ENUM('PENDING', 'CLOSED', 'CANCELLED', 'ON_HOLD', 'REJECTED') NOT NULL DEFAULT 'PENDING',
    notes TEXT,
    commission_calculated BOOLEAN NOT NULL DEFAULT FALSE,
    commission_paid BOOLEAN NOT NULL DEFAULT FALSE,
    payment_date DATETIME,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    version BIGINT DEFAULT 0,
    
    FOREIGN KEY (agent_id) REFERENCES users(id) ON DELETE RESTRICT,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT,
    INDEX idx_deal_agent (agent_id),
    INDEX idx_deal_product (product_id),
    INDEX idx_deal_status (status),
    INDEX idx_deal_date (deal_date),
    INDEX idx_deal_reference (deal_reference),
    INDEX idx_deal_commission_calc (commission_calculated),
    INDEX idx_deal_commission_paid (commission_paid)
);

-- Create commissions table
CREATE TABLE commissions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    agent_id BIGINT NOT NULL,
    deal_id BIGINT NOT NULL,
    commission_rule_id BIGINT,
    commission_amount DECIMAL(15,2) NOT NULL,
    commission_rate DECIMAL(5,2),
    calculated_date DATETIME NOT NULL,
    paid_date DATETIME,
    status ENUM('CALCULATED', 'APPROVED', 'PAID', 'REJECTED', 'ON_HOLD') NOT NULL DEFAULT 'CALCULATED',
    commission_period VARCHAR(7) NOT NULL, -- Format: YYYY-MM
    notes TEXT,
    payment_reference VARCHAR(100),
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    version BIGINT DEFAULT 0,
    
    FOREIGN KEY (agent_id) REFERENCES users(id) ON DELETE RESTRICT,
    FOREIGN KEY (deal_id) REFERENCES deals(id) ON DELETE RESTRICT,
    FOREIGN KEY (commission_rule_id) REFERENCES commission_rules(id) ON DELETE SET NULL,
    INDEX idx_commission_agent (agent_id),
    INDEX idx_commission_deal (deal_id),
    INDEX idx_commission_status (status),
    INDEX idx_commission_period (commission_period),
    INDEX idx_commission_calculated_date (calculated_date),
    INDEX idx_commission_paid_date (paid_date)
);

-- Create audit log table for tracking changes
CREATE TABLE audit_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    table_name VARCHAR(50) NOT NULL,
    record_id BIGINT NOT NULL,
    action ENUM('INSERT', 'UPDATE', 'DELETE') NOT NULL,
    old_values JSON,
    new_values JSON,
    user_id BIGINT,
    timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_audit_table_record (table_name, record_id),
    INDEX idx_audit_user (user_id),
    INDEX idx_audit_timestamp (timestamp)
);

-- Create refresh tokens table for JWT management
CREATE TABLE refresh_tokens (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    expires_at DATETIME NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_refresh_token_user (user_id),
    INDEX idx_refresh_token_expires (expires_at)
);

-- Create system settings table
CREATE TABLE system_settings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    description TEXT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_system_settings_key (setting_key)
);

-- Create notifications table
CREATE TABLE notifications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('INFO', 'SUCCESS', 'WARNING', 'ERROR') NOT NULL DEFAULT 'INFO',
    is_read BOOLEAN NOT NULL DEFAULT FALSE,
    action_url VARCHAR(500),
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_notification_user (user_id),
    INDEX idx_notification_read (is_read),
    INDEX idx_notification_created (created_at)
);

-- Create performance metrics view
CREATE VIEW agent_performance AS
SELECT 
    u.id as agent_id,
    u.first_name,
    u.last_name,
    u.email,
    COUNT(d.id) as total_deals,
    COUNT(CASE WHEN d.status = 'CLOSED' THEN 1 END) as closed_deals,
    COALESCE(SUM(CASE WHEN d.status = 'CLOSED' THEN d.deal_value ELSE 0 END), 0) as total_deal_value,
    COALESCE(SUM(c.commission_amount), 0) as total_commission,
    COALESCE(SUM(CASE WHEN c.status = 'PAID' THEN c.commission_amount ELSE 0 END), 0) as paid_commission,
    COALESCE(AVG(CASE WHEN d.status = 'CLOSED' THEN d.deal_value END), 0) as avg_deal_value
FROM users u
LEFT JOIN deals d ON u.id = d.agent_id
LEFT JOIN commissions c ON d.id = c.deal_id
WHERE u.role = 'SALES_AGENT'
GROUP BY u.id, u.first_name, u.last_name, u.email;

-- Create monthly commission summary view
CREATE VIEW monthly_commission_summary AS
SELECT 
    c.commission_period,
    c.agent_id,
    u.first_name,
    u.last_name,
    COUNT(c.id) as commission_count,
    COALESCE(SUM(c.commission_amount), 0) as total_amount,
    COALESCE(SUM(CASE WHEN c.status = 'PAID' THEN c.commission_amount ELSE 0 END), 0) as paid_amount,
    COALESCE(SUM(CASE WHEN c.status IN ('CALCULATED', 'APPROVED') THEN c.commission_amount ELSE 0 END), 0) as pending_amount
FROM commissions c
JOIN users u ON c.agent_id = u.id
GROUP BY c.commission_period, c.agent_id, u.first_name, u.last_name
ORDER BY c.commission_period DESC, total_amount DESC;
