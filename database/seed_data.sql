-- QuotaSnap Sample Data
-- Insert sample data for development and testing

USE quotasnap_db;

-- Insert sample users
INSERT INTO users (first_name, last_name, email, password_hash, role, status, employee_id, department, hire_date) VALUES
('<PERSON>', 'Admin', '<EMAIL>', '$2a$10$example_hash_admin', 'ADMIN', 'ACTIVE', 'EMP001', 'Administration', '2023-01-15 09:00:00'),
('<PERSON>', 'Manager', '<EMAIL>', '$2a$10$example_hash_manager', 'MANAGER', 'ACTIVE', 'EMP002', 'Sales', '2023-02-01 09:00:00'),
('<PERSON>', '<PERSON>', '<EMAIL>', '$2a$10$example_hash_agent1', 'SALES_AGENT', 'ACTIVE', 'EMP003', 'Sales', '2023-03-01 09:00:00'),
('<PERSON>', '<PERSON>', '<EMAIL>', '$2a$10$example_hash_agent2', 'SALES_AGENT', 'ACTIVE', 'EMP004', 'Sales', '2023-03-15 09:00:00'),
('<PERSON>', '<PERSON>', '<EMAIL>', '$2a$10$example_hash_agent3', 'SALES_AGENT', 'ACTIVE', 'EMP005', 'Sales', '2023-04-01 09:00:00'),
('Lisa', 'Finance', '<EMAIL>', '$2a$10$example_hash_finance', 'FINANCE', 'ACTIVE', 'EMP006', 'Finance', '2023-02-15 09:00:00');

-- Update manager relationships
UPDATE users SET manager_id = 2 WHERE id IN (3, 4, 5); -- Sales agents report to Sarah Manager

-- Insert sample products
INSERT INTO products (name, product_code, description, category, base_price, status, is_commission_eligible) VALUES
('Premium Insurance Policy', 'INS-PREM-001', 'Comprehensive insurance coverage with premium benefits', 'Insurance', 5000.00, 'ACTIVE', TRUE),
('Basic Insurance Policy', 'INS-BASIC-001', 'Essential insurance coverage for individuals', 'Insurance', 2000.00, 'ACTIVE', TRUE),
('Investment Portfolio A', 'INV-PORT-A', 'Diversified investment portfolio for high-net-worth clients', 'Investment', 50000.00, 'ACTIVE', TRUE),
('Investment Portfolio B', 'INV-PORT-B', 'Balanced investment portfolio for moderate risk tolerance', 'Investment', 25000.00, 'ACTIVE', TRUE),
('Real Estate Package', 'RE-PKG-001', 'Complete real estate investment package', 'Real Estate', 100000.00, 'ACTIVE', TRUE),
('Financial Advisory Service', 'FIN-ADV-001', 'Professional financial planning and advisory services', 'Advisory', 10000.00, 'ACTIVE', TRUE);

-- Insert sample commission rules
INSERT INTO commission_rules (product_id, commission_type, commission_rate, tier_level, min_deal_value, max_deal_value, effective_from, is_active, description) VALUES
-- Premium Insurance Policy rules
(1, 'PERCENTAGE', 5.00, 1, 0, 10000, '2023-01-01 00:00:00', TRUE, '5% commission for deals up to $10,000'),
(1, 'PERCENTAGE', 7.00, 2, 10001, 25000, '2023-01-01 00:00:00', TRUE, '7% commission for deals $10,001-$25,000'),
(1, 'PERCENTAGE', 10.00, 3, 25001, NULL, '2023-01-01 00:00:00', TRUE, '10% commission for deals above $25,000'),

-- Basic Insurance Policy rules
(2, 'PERCENTAGE', 3.00, 1, 0, 5000, '2023-01-01 00:00:00', TRUE, '3% commission for deals up to $5,000'),
(2, 'PERCENTAGE', 5.00, 2, 5001, NULL, '2023-01-01 00:00:00', TRUE, '5% commission for deals above $5,000'),

-- Investment Portfolio A rules
(3, 'PERCENTAGE', 2.00, 1, 0, 100000, '2023-01-01 00:00:00', TRUE, '2% commission for deals up to $100,000'),
(3, 'PERCENTAGE', 3.00, 2, 100001, NULL, '2023-01-01 00:00:00', TRUE, '3% commission for deals above $100,000'),

-- Investment Portfolio B rules
(4, 'PERCENTAGE', 2.50, 1, 0, NULL, '2023-01-01 00:00:00', TRUE, '2.5% commission for all deals'),

-- Real Estate Package rules
(5, 'PERCENTAGE', 1.50, 1, 0, 500000, '2023-01-01 00:00:00', TRUE, '1.5% commission for deals up to $500,000'),
(5, 'PERCENTAGE', 2.00, 2, 500001, NULL, '2023-01-01 00:00:00', TRUE, '2% commission for deals above $500,000'),

-- Financial Advisory Service rules
(6, 'FIXED', 0.00, 1, 0, NULL, '2023-01-01 00:00:00', TRUE, 'Fixed $500 commission per deal');

-- Update the fixed amount for Financial Advisory Service
UPDATE commission_rules SET fixed_amount = 500.00 WHERE product_id = 6;

-- Insert sample deals
INSERT INTO deals (deal_reference, agent_id, product_id, client_name, client_email, client_phone, deal_value, quantity, deal_date, close_date, status, commission_calculated, commission_paid) VALUES
-- Mike Johnson's deals
('DEAL-2024-001', 3, 1, 'ABC Corporation', '<EMAIL>', '******-0101', 15000.00, 1, '2024-01-15 10:30:00', '2024-01-20 14:00:00', 'CLOSED', TRUE, TRUE),
('DEAL-2024-002', 3, 2, 'John Smith', '<EMAIL>', '******-0102', 3500.00, 1, '2024-01-22 09:15:00', '2024-01-25 16:30:00', 'CLOSED', TRUE, TRUE),
('DEAL-2024-003', 3, 3, 'Investment Group LLC', '<EMAIL>', '******-0103', 75000.00, 1, '2024-02-01 11:00:00', '2024-02-05 13:45:00', 'CLOSED', TRUE, FALSE),
('DEAL-2024-004', 3, 1, 'Tech Startup Inc', '<EMAIL>', '******-0104', 8000.00, 1, '2024-02-10 14:20:00', NULL, 'PENDING', FALSE, FALSE),

-- Emily Davis's deals
('DEAL-2024-005', 4, 2, 'Mary Johnson', '<EMAIL>', '******-0201', 4500.00, 1, '2024-01-18 13:30:00', '2024-01-22 10:15:00', 'CLOSED', TRUE, TRUE),
('DEAL-2024-006', 4, 4, 'Retirement Planning Co', '<EMAIL>', '******-0202', 30000.00, 1, '2024-01-28 15:45:00', '2024-02-02 12:00:00', 'CLOSED', TRUE, TRUE),
('DEAL-2024-007', 4, 1, 'Global Enterprises', '<EMAIL>', '******-0203', 22000.00, 1, '2024-02-05 09:30:00', '2024-02-08 16:20:00', 'CLOSED', TRUE, FALSE),
('DEAL-2024-008', 4, 6, 'Wealthy Family Trust', '<EMAIL>', '******-0204', 12000.00, 1, '2024-02-12 11:15:00', NULL, 'PENDING', FALSE, FALSE),

-- David Wilson's deals
('DEAL-2024-009', 5, 5, 'Property Investors LLC', '<EMAIL>', '******-0301', 250000.00, 1, '2024-01-20 10:00:00', '2024-01-25 15:30:00', 'CLOSED', TRUE, TRUE),
('DEAL-2024-010', 5, 3, 'High Net Worth Client', '<EMAIL>', '******-0302', 120000.00, 1, '2024-02-01 14:30:00', '2024-02-06 11:45:00', 'CLOSED', TRUE, FALSE),
('DEAL-2024-011', 5, 1, 'Insurance Seekers Inc', '<EMAIL>', '******-0303', 18000.00, 1, '2024-02-08 12:20:00', NULL, 'PENDING', FALSE, FALSE);

-- Insert sample commissions (calculated for closed deals)
INSERT INTO commissions (agent_id, deal_id, commission_rule_id, commission_amount, commission_rate, calculated_date, paid_date, status, commission_period, payment_reference) VALUES
-- Mike Johnson's commissions
(3, 1, 2, 1050.00, 7.00, '2024-01-20 14:30:00', '2024-01-31 10:00:00', 'PAID', '2024-01', 'PAY-2024-001'),
(3, 2, 5, 175.00, 5.00, '2024-01-25 17:00:00', '2024-01-31 10:00:00', 'PAID', '2024-01', 'PAY-2024-002'),
(3, 3, 7, 2250.00, 3.00, '2024-02-05 14:15:00', NULL, 'APPROVED', '2024-02', NULL),

-- Emily Davis's commissions
(4, 5, 4, 135.00, 3.00, '2024-01-22 10:45:00', '2024-01-31 10:00:00', 'PAID', '2024-01', 'PAY-2024-003'),
(4, 6, 8, 750.00, 2.50, '2024-02-02 12:30:00', '2024-02-28 10:00:00', 'PAID', '2024-02', 'PAY-2024-004'),
(4, 7, 2, 1540.00, 7.00, '2024-02-08 16:50:00', NULL, 'APPROVED', '2024-02', NULL),

-- David Wilson's commissions
(5, 9, 9, 3750.00, 1.50, '2024-01-25 16:00:00', '2024-01-31 10:00:00', 'PAID', '2024-01', 'PAY-2024-005'),
(5, 10, 7, 3600.00, 3.00, '2024-02-06 12:15:00', NULL, 'APPROVED', '2024-02', NULL);

-- Insert system settings
INSERT INTO system_settings (setting_key, setting_value, description) VALUES
('commission_calculation_enabled', 'true', 'Enable automatic commission calculation'),
('commission_approval_required', 'true', 'Require manager approval for commission payments'),
('max_commission_rate', '15.00', 'Maximum commission rate allowed (percentage)'),
('min_deal_value', '100.00', 'Minimum deal value for commission eligibility'),
('notification_email_enabled', 'true', 'Enable email notifications'),
('app_version', '1.0.0', 'Current application version'),
('maintenance_mode', 'false', 'Application maintenance mode flag');

-- Insert sample notifications
INSERT INTO notifications (user_id, title, message, type, is_read) VALUES
(3, 'Commission Paid', 'Your commission of $1,050.00 for deal DEAL-2024-001 has been paid.', 'SUCCESS', TRUE),
(3, 'New Commission Approved', 'Your commission of $2,250.00 for deal DEAL-2024-003 has been approved for payment.', 'INFO', FALSE),
(4, 'Commission Paid', 'Your commission of $750.00 for deal DEAL-2024-006 has been paid.', 'SUCCESS', TRUE),
(4, 'Deal Pending Review', 'Your deal DEAL-2024-008 is pending manager review.', 'WARNING', FALSE),
(5, 'High Value Deal Closed', 'Congratulations! You closed a high-value deal worth $250,000.', 'SUCCESS', TRUE),
(2, 'Team Performance Update', 'Your team has achieved 95% of the monthly target.', 'INFO', FALSE);

-- Create indexes for better performance (if not already created in schema)
-- These are additional indexes for common query patterns

-- Index for commission calculations
CREATE INDEX idx_deals_commission_calc ON deals (status, commission_calculated, product_id);

-- Index for performance queries
CREATE INDEX idx_commissions_agent_period ON commissions (agent_id, commission_period, status);

-- Index for dashboard queries
CREATE INDEX idx_deals_agent_date_status ON deals (agent_id, deal_date, status);

-- Composite index for leaderboard queries
CREATE INDEX idx_commissions_status_amount ON commissions (status, commission_amount DESC);
