# QuotaSnap Setup Guide

This guide will help you set up the QuotaSnap Sales Commission Tracking App on your local development environment.

## Prerequisites

### Required Software
- **Node.js** (v16 or higher)
- **Java** (JDK 17 or higher)
- **MySQL** (8.0 or higher)
- **Android Studio** (for Android development)
- **Xcode** (for iOS development - macOS only)
- **Git**

### Development Tools
- **VS Code** or **IntelliJ IDEA**
- **<PERSON>man** (for API testing)
- **MySQL Workbench** (optional)

## Project Structure

```
QuotaSnap/
├── backend/                 # Spring Boot API
│   ├── src/main/java/
│   ├── src/main/resources/
│   └── pom.xml
├── mobile/                  # React Native App
│   ├── src/
│   ├── android/
│   ├── ios/
│   └── package.json
├── database/               # MySQL schemas and migrations
│   ├── schema.sql
│   └── seed_data.sql
├── docs/                   # Documentation
├── README.md
└── setup.md
```

## Setup Instructions

### 1. Database Setup

1. **Install MySQL 8.0+**
   ```bash
   # On macOS with Homebrew
   brew install mysql
   
   # On Ubuntu
   sudo apt-get install mysql-server
   
   # On Windows
   # Download from https://dev.mysql.com/downloads/mysql/
   ```

2. **Start MySQL Service**
   ```bash
   # On macOS
   brew services start mysql
   
   # On Ubuntu
   sudo systemctl start mysql
   
   # On Windows
   # Use MySQL Workbench or Services
   ```

3. **Create Database and User**
   ```sql
   mysql -u root -p
   
   CREATE DATABASE quotasnap_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   CREATE USER 'quotasnap_user'@'localhost' IDENTIFIED BY 'quotasnap_pass';
   GRANT ALL PRIVILEGES ON quotasnap_db.* TO 'quotasnap_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

4. **Run Database Schema**
   ```bash
   cd database
   mysql -u quotasnap_user -p quotasnap_db < schema.sql
   mysql -u quotasnap_user -p quotasnap_db < seed_data.sql
   ```

### 2. Backend Setup (Spring Boot)

1. **Navigate to Backend Directory**
   ```bash
   cd backend
   ```

2. **Configure Application Properties**
   Create `src/main/resources/application-local.yml`:
   ```yaml
   spring:
     datasource:
       url: ****************************************
       username: quotasnap_user
       password: quotasnap_pass
     
   jwt:
     secret: your-secret-key-here-make-it-long-and-secure
   
   app:
     cors:
       allowed-origins: http://localhost:3000,http://localhost:19006
   ```

3. **Install Dependencies and Run**
   ```bash
   # Install dependencies
   ./mvnw clean install
   
   # Run the application
   ./mvnw spring-boot:run -Dspring-boot.run.profiles=local
   ```

4. **Verify Backend**
   - API should be running on `http://localhost:8080`
   - Swagger UI: `http://localhost:8080/swagger-ui.html`
   - Health check: `http://localhost:8080/actuator/health`

### 3. Mobile App Setup (React Native)

1. **Navigate to Mobile Directory**
   ```bash
   cd mobile
   ```

2. **Install Dependencies**
   ```bash
   npm install
   
   # For iOS (macOS only)
   cd ios && pod install && cd ..
   ```

3. **Configure Environment**
   Create `.env` file:
   ```
   API_BASE_URL=http://localhost:8080/api
   GOOGLE_CLIENT_ID=your-google-client-id
   GITHUB_CLIENT_ID=your-github-client-id
   ```

4. **Start Metro Bundler**
   ```bash
   npm start
   ```

5. **Run on Device/Emulator**
   ```bash
   # Android
   npm run android
   
   # iOS (macOS only)
   npm run ios
   ```

### 4. Development Environment Setup

#### Android Development
1. **Install Android Studio**
2. **Set up Android SDK**
3. **Create Virtual Device (AVD)**
4. **Set Environment Variables**
   ```bash
   export ANDROID_HOME=$HOME/Library/Android/sdk
   export PATH=$PATH:$ANDROID_HOME/emulator
   export PATH=$PATH:$ANDROID_HOME/tools
   export PATH=$PATH:$ANDROID_HOME/tools/bin
   export PATH=$PATH:$ANDROID_HOME/platform-tools
   ```

#### iOS Development (macOS only)
1. **Install Xcode from App Store**
2. **Install Xcode Command Line Tools**
   ```bash
   xcode-select --install
   ```
3. **Install CocoaPods**
   ```bash
   sudo gem install cocoapods
   ```

## Configuration

### Backend Configuration

#### Database Configuration
- Update `application.yml` with your database credentials
- Configure connection pool settings for production

#### Security Configuration
- Generate secure JWT secret key
- Configure OAuth2 providers (Google, GitHub)
- Set up CORS for your frontend domains

#### Email Configuration (Optional)
```yaml
spring:
  mail:
    host: smtp.gmail.com
    port: 587
    username: <EMAIL>
    password: your-app-password
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
```

### Mobile Configuration

#### API Configuration
- Update `src/config/api.ts` with your backend URL
- Configure timeout and retry settings

#### OAuth Configuration
- Set up Google Sign-In
- Configure GitHub OAuth (if needed)

#### Push Notifications (Optional)
- Configure Firebase for push notifications
- Set up notification handlers

## Testing

### Backend Testing
```bash
cd backend
./mvnw test
```

### Mobile Testing
```bash
cd mobile
npm test
```

### API Testing
- Import Postman collection from `docs/postman/`
- Test all endpoints with sample data

## Deployment

### Backend Deployment
1. **Build Production JAR**
   ```bash
   ./mvnw clean package -Pprod
   ```

2. **Deploy to Server**
   - Use Docker for containerization
   - Configure production database
   - Set up reverse proxy (Nginx)

### Mobile Deployment
1. **Android**
   ```bash
   npm run build:android
   ```

2. **iOS**
   ```bash
   npm run build:ios
   ```

## Troubleshooting

### Common Issues

#### Backend Issues
- **Database Connection**: Check MySQL service and credentials
- **Port Conflicts**: Change server port in `application.yml`
- **Memory Issues**: Increase JVM heap size

#### Mobile Issues
- **Metro Bundler**: Clear cache with `npx react-native start --reset-cache`
- **Android Build**: Clean and rebuild with `cd android && ./gradlew clean`
- **iOS Build**: Clean build folder in Xcode

#### Network Issues
- **CORS Errors**: Update allowed origins in backend configuration
- **API Connection**: Check firewall and network settings

### Getting Help
- Check the logs in `logs/quotasnap.log`
- Use browser developer tools for frontend issues
- Check React Native debugger for mobile issues

## Development Workflow

### Git Workflow
1. Create feature branch from `develop`
2. Make changes and commit with conventional commit messages
3. Create pull request for code review
4. Merge to `develop` after approval
5. Deploy to staging for testing
6. Merge to `main` for production release

### Code Quality
- Follow ESLint rules for JavaScript/TypeScript
- Use Prettier for code formatting
- Follow Java coding standards
- Write unit tests for new features
- Update documentation for API changes

## Next Steps

1. **Set up CI/CD pipeline**
2. **Configure monitoring and logging**
3. **Set up backup strategies**
4. **Implement security best practices**
5. **Add performance monitoring**
6. **Set up error tracking**

For more detailed information, check the individual README files in each directory.
