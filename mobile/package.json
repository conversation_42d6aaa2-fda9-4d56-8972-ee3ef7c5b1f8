{"name": "quotasnap-mobile", "version": "1.0.0", "description": "QuotaSnap Sales Commission Tracking Mobile App", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace QuotaSnap.xcworkspace -scheme QuotaSnap -configuration Release -destination generic/platform=iOS -archivePath QuotaSnap.xcarchive archive"}, "dependencies": {"react": "18.2.0", "react-native": "0.72.6", "@react-navigation/native": "^6.1.9", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/stack": "^6.3.20", "@react-navigation/drawer": "^6.6.6", "react-native-screens": "^3.27.0", "react-native-safe-area-context": "^4.7.4", "react-native-gesture-handler": "^2.13.4", "react-native-reanimated": "^3.5.4", "@react-native-async-storage/async-storage": "^1.19.5", "react-native-vector-icons": "^10.0.2", "@react-native-community/netinfo": "^9.4.1", "react-native-chart-kit": "^6.12.0", "react-native-svg": "^13.14.0", "react-native-paper": "^5.11.1", "react-native-elements": "^3.4.3", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "^13.0.1", "react-native-date-picker": "^4.3.3", "react-native-picker-select": "^8.1.0", "react-native-image-picker": "^7.0.3", "react-native-document-picker": "^9.1.1", "react-native-share": "^9.4.1", "react-native-print": "^0.8.0", "axios": "^1.6.0", "react-query": "^3.39.3", "formik": "^2.4.5", "yup": "^1.3.3", "react-native-keychain": "^8.1.3", "react-native-biometrics": "^3.0.1", "@react-native-google-signin/google-signin": "^10.1.0", "react-native-config": "^1.5.1", "react-native-splash-screen": "^3.3.0", "react-native-orientation-locker": "^1.5.0", "react-native-device-info": "^10.11.0", "react-native-permissions": "^3.10.1", "react-native-toast-message": "^2.1.6", "react-native-skeleton-placeholder": "^5.2.4", "react-native-pull-to-refresh": "^2.1.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4", "@types/react-native-vector-icons": "^6.4.15"}, "engines": {"node": ">=16"}, "keywords": ["react-native", "sales", "commission", "tracking", "mobile", "fintech"], "author": "QuotaSnap Development Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/quotasnap/quotasnap-mobile.git"}, "bugs": {"url": "https://github.com/quotasnap/quotasnap-mobile/issues"}, "homepage": "https://github.com/quotasnap/quotasnap-mobile#readme"}