import { DefaultTheme } from 'react-native-paper';

// QuotaSnap Color Scheme: Indigo, White, Neon Orange, Charcoal
export const colors = {
  primary: '#3F51B5',      // Indigo
  secondary: '#FF6D00',    // Neon Orange
  background: '#FFFFFF',   // White
  surface: '#FFFFFF',      // White
  text: '#424242',         // Charcoal
  textSecondary: '#757575',
  textLight: '#9E9E9E',
  accent: '#FF6D00',       // Neon Orange
  error: '#F44336',
  warning: '#FF9800',
  success: '#4CAF50',
  info: '#2196F3',
  
  // Gradient colors
  primaryGradient: ['#3F51B5', '#5C6BC0'],
  secondaryGradient: ['#FF6D00', '#FF8F00'],
  
  // Background variations
  backgroundLight: '#FAFAFA',
  backgroundDark: '#F5F5F5',
  
  // Border colors
  border: '#E0E0E0',
  borderLight: '#F0F0F0',
  
  // Shadow colors
  shadow: '#000000',
  
  // Status colors
  online: '#4CAF50',
  offline: '#9E9E9E',
  pending: '#FF9800',
  approved: '#4CAF50',
  rejected: '#F44336',
  
  // Chart colors
  chart: {
    primary: '#3F51B5',
    secondary: '#FF6D00',
    tertiary: '#4CAF50',
    quaternary: '#2196F3',
    quinary: '#9C27B0',
  },
};

export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

export const borderRadius = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  round: 50,
};

export const typography = {
  fontFamily: {
    regular: 'System',
    medium: 'System',
    bold: 'System',
  },
  fontSize: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 28,
  },
  lineHeight: {
    xs: 16,
    sm: 20,
    md: 24,
    lg: 28,
    xl: 32,
    xxl: 36,
    xxxl: 42,
  },
};

export const shadows = {
  small: {
    shadowColor: colors.shadow,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  medium: {
    shadowColor: colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 4,
  },
  large: {
    shadowColor: colors.shadow,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
};

// React Native Paper theme configuration
export const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: colors.primary,
    accent: colors.accent,
    background: colors.background,
    surface: colors.surface,
    text: colors.text,
    error: colors.error,
    onSurface: colors.text,
    placeholder: colors.textLight,
  },
  roundness: borderRadius.md,
};

// Custom theme object for our components
export const customTheme = {
  colors,
  spacing,
  borderRadius,
  typography,
  shadows,
};

export type Theme = typeof customTheme;
