import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  RefreshControl,
  Dimensions,
} from 'react-native';
import {
  Card,
  Title,
  Paragraph,
  Button,
  FAB,
  Portal,
  Modal,
} from 'react-native-paper';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-chart-kit';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { colors, spacing, shadows } from '../theme/theme';
import { useAuth } from '../context/AuthContext';
import { useDashboardData } from '../hooks/useDashboardData';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';
import StatsCard from '../components/StatsCard';
import RecentDealsCard from '../components/RecentDealsCard';
import { MainStackParamList } from '../navigation/MainNavigator';

type DashboardNavigationProp = StackNavigationProp<MainStackParamList>;

const { width: screenWidth } = Dimensions.get('window');

const DashboardScreen: React.FC = () => {
  const navigation = useNavigation<DashboardNavigationProp>();
  const { state: authState } = useAuth();
  const [refreshing, setRefreshing] = useState(false);
  const [fabOpen, setFabOpen] = useState(false);

  const {
    data: dashboardData,
    isLoading,
    error,
    refetch,
  } = useDashboardData(authState.user?.id);

  const onRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const chartConfig = {
    backgroundColor: colors.background,
    backgroundGradientFrom: colors.background,
    backgroundGradientTo: colors.background,
    decimalPlaces: 0,
    color: (opacity = 1) => `rgba(63, 81, 181, ${opacity})`,
    labelColor: (opacity = 1) => `rgba(66, 66, 66, ${opacity})`,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '6',
      strokeWidth: '2',
      stroke: colors.primary,
    },
  };

  if (isLoading && !dashboardData) {
    return <LoadingSpinner />;
  }

  if (error) {
    return <ErrorMessage message="Failed to load dashboard data" onRetry={refetch} />;
  }

  const commissionData = dashboardData?.commissionTrend || [];
  const dealStatusData = dashboardData?.dealStatusBreakdown || [];

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Welcome Section */}
        <Card style={styles.welcomeCard}>
          <Card.Content>
            <Title style={styles.welcomeTitle}>
              Welcome back, {authState.user?.firstName}!
            </Title>
            <Paragraph style={styles.welcomeSubtitle}>
              Here's your performance overview
            </Paragraph>
          </Card.Content>
        </Card>

        {/* Stats Overview */}
        <View style={styles.statsContainer}>
          <StatsCard
            title="Total Commission"
            value={`$${dashboardData?.totalCommission?.toLocaleString() || '0'}`}
            icon="attach-money"
            color={colors.success}
            trend={dashboardData?.commissionTrend?.length > 1 ? 'up' : 'neutral'}
          />
          <StatsCard
            title="This Month"
            value={`$${dashboardData?.monthlyCommission?.toLocaleString() || '0'}`}
            icon="trending-up"
            color={colors.primary}
            trend="up"
          />
          <StatsCard
            title="Deals Closed"
            value={dashboardData?.totalDeals?.toString() || '0'}
            icon="business-center"
            color={colors.secondary}
            trend="up"
          />
          <StatsCard
            title="Pending"
            value={dashboardData?.pendingDeals?.toString() || '0'}
            icon="schedule"
            color={colors.warning}
            trend="neutral"
          />
        </View>

        {/* Commission Trend Chart */}
        {commissionData.length > 0 && (
          <Card style={styles.chartCard}>
            <Card.Content>
              <Title style={styles.chartTitle}>Commission Trend</Title>
              <LineChart
                data={{
                  labels: commissionData.map(item => item.month),
                  datasets: [
                    {
                      data: commissionData.map(item => item.amount),
                    },
                  ],
                }}
                width={screenWidth - 60}
                height={220}
                chartConfig={chartConfig}
                bezier
                style={styles.chart}
              />
            </Card.Content>
          </Card>
        )}

        {/* Deal Status Breakdown */}
        {dealStatusData.length > 0 && (
          <Card style={styles.chartCard}>
            <Card.Content>
              <Title style={styles.chartTitle}>Deal Status Breakdown</Title>
              <PieChart
                data={dealStatusData.map((item, index) => ({
                  name: item.status,
                  population: item.count,
                  color: colors.chart[Object.keys(colors.chart)[index] as keyof typeof colors.chart],
                  legendFontColor: colors.text,
                  legendFontSize: 12,
                }))}
                width={screenWidth - 60}
                height={220}
                chartConfig={chartConfig}
                accessor="population"
                backgroundColor="transparent"
                paddingLeft="15"
                style={styles.chart}
              />
            </Card.Content>
          </Card>
        )}

        {/* Recent Deals */}
        <RecentDealsCard
          deals={dashboardData?.recentDeals || []}
          onViewAll={() => navigation.navigate('Deals')}
        />

        {/* Quick Actions */}
        <Card style={styles.actionsCard}>
          <Card.Content>
            <Title style={styles.actionsTitle}>Quick Actions</Title>
            <View style={styles.actionsContainer}>
              <Button
                mode="contained"
                icon="add"
                onPress={() => navigation.navigate('AddDeal')}
                style={styles.actionButton}
              >
                Add Deal
              </Button>
              <Button
                mode="outlined"
                icon="calculate"
                onPress={() => navigation.navigate('Calculator')}
                style={styles.actionButton}
              >
                Calculator
              </Button>
              <Button
                mode="outlined"
                icon="assessment"
                onPress={() => navigation.navigate('Reports')}
                style={styles.actionButton}
              >
                Reports
              </Button>
            </View>
          </Card.Content>
        </Card>

        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Floating Action Button */}
      <Portal>
        <FAB.Group
          open={fabOpen}
          icon={fabOpen ? 'close' : 'add'}
          actions={[
            {
              icon: 'business-center',
              label: 'Add Deal',
              onPress: () => navigation.navigate('AddDeal'),
            },
            {
              icon: 'calculate',
              label: 'Calculator',
              onPress: () => navigation.navigate('Calculator'),
            },
            {
              icon: 'assessment',
              label: 'Reports',
              onPress: () => navigation.navigate('Reports'),
            },
          ]}
          onStateChange={({ open }) => setFabOpen(open)}
          onPress={() => {
            if (fabOpen) {
              // do something if the speed dial is open
            }
          }}
          fabStyle={{
            backgroundColor: colors.primary,
          }}
        />
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundLight,
  },
  scrollView: {
    flex: 1,
  },
  welcomeCard: {
    margin: spacing.md,
    marginBottom: spacing.sm,
    ...shadows.small,
  },
  welcomeTitle: {
    color: colors.text,
    fontSize: 24,
    fontWeight: 'bold',
  },
  welcomeSubtitle: {
    color: colors.textSecondary,
    fontSize: 16,
    marginTop: spacing.xs,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: spacing.md,
    justifyContent: 'space-between',
  },
  chartCard: {
    margin: spacing.md,
    marginBottom: spacing.sm,
    ...shadows.small,
  },
  chartTitle: {
    color: colors.text,
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: spacing.md,
  },
  chart: {
    marginVertical: spacing.sm,
    borderRadius: 16,
  },
  actionsCard: {
    margin: spacing.md,
    marginBottom: spacing.sm,
    ...shadows.small,
  },
  actionsTitle: {
    color: colors.text,
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: spacing.md,
  },
  actionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionButton: {
    marginBottom: spacing.sm,
    minWidth: '30%',
  },
  bottomSpacing: {
    height: 100,
  },
});

export default DashboardScreen;
