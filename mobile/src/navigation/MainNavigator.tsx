import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors } from '../theme/theme';

// Screens
import DashboardScreen from '../screens/DashboardScreen';
import CommissionCalculatorScreen from '../screens/CommissionCalculatorScreen';
import LeaderboardScreen from '../screens/LeaderboardScreen';
import DealsScreen from '../screens/DealsScreen';
import ProfileScreen from '../screens/ProfileScreen';
import DealDetailsScreen from '../screens/DealDetailsScreen';
import AddDealScreen from '../screens/AddDealScreen';
import EditDealScreen from '../screens/EditDealScreen';
import CommissionDetailsScreen from '../screens/CommissionDetailsScreen';
import ReportsScreen from '../screens/ReportsScreen';
import SettingsScreen from '../screens/SettingsScreen';

export type MainTabParamList = {
  Dashboard: undefined;
  Calculator: undefined;
  Leaderboard: undefined;
  Deals: undefined;
  Profile: undefined;
};

export type MainStackParamList = {
  MainTabs: undefined;
  DealDetails: { dealId: string };
  AddDeal: undefined;
  EditDeal: { dealId: string };
  CommissionDetails: { commissionId: string };
  Reports: undefined;
  Settings: undefined;
};

const Tab = createBottomTabNavigator<MainTabParamList>();
const Stack = createStackNavigator<MainStackParamList>();

const MainTabs: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'Dashboard':
              iconName = 'dashboard';
              break;
            case 'Calculator':
              iconName = 'calculate';
              break;
            case 'Leaderboard':
              iconName = 'leaderboard';
              break;
            case 'Deals':
              iconName = 'business-center';
              break;
            case 'Profile':
              iconName = 'person';
              break;
            default:
              iconName = 'help';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.textLight,
        tabBarStyle: {
          backgroundColor: colors.background,
          borderTopColor: colors.border,
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        },
        headerStyle: {
          backgroundColor: colors.primary,
        },
        headerTintColor: colors.background,
        headerTitleStyle: {
          fontWeight: 'bold',
          fontSize: 18,
        },
      })}
    >
      <Tab.Screen
        name="Dashboard"
        component={DashboardScreen}
        options={{
          title: 'Dashboard',
          headerTitle: 'QuotaSnap Dashboard',
        }}
      />
      <Tab.Screen
        name="Calculator"
        component={CommissionCalculatorScreen}
        options={{
          title: 'Calculator',
          headerTitle: 'Commission Calculator',
        }}
      />
      <Tab.Screen
        name="Leaderboard"
        component={LeaderboardScreen}
        options={{
          title: 'Leaderboard',
          headerTitle: 'Performance Leaderboard',
        }}
      />
      <Tab.Screen
        name="Deals"
        component={DealsScreen}
        options={{
          title: 'Deals',
          headerTitle: 'My Deals',
        }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          title: 'Profile',
          headerTitle: 'My Profile',
        }}
      />
    </Tab.Navigator>
  );
};

const MainNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.primary,
        },
        headerTintColor: colors.background,
        headerTitleStyle: {
          fontWeight: 'bold',
          fontSize: 18,
        },
      }}
    >
      <Stack.Screen
        name="MainTabs"
        component={MainTabs}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="DealDetails"
        component={DealDetailsScreen}
        options={{
          title: 'Deal Details',
          headerBackTitleVisible: false,
        }}
      />
      <Stack.Screen
        name="AddDeal"
        component={AddDealScreen}
        options={{
          title: 'Add New Deal',
          headerBackTitleVisible: false,
        }}
      />
      <Stack.Screen
        name="EditDeal"
        component={EditDealScreen}
        options={{
          title: 'Edit Deal',
          headerBackTitleVisible: false,
        }}
      />
      <Stack.Screen
        name="CommissionDetails"
        component={CommissionDetailsScreen}
        options={{
          title: 'Commission Details',
          headerBackTitleVisible: false,
        }}
      />
      <Stack.Screen
        name="Reports"
        component={ReportsScreen}
        options={{
          title: 'Reports & Analytics',
          headerBackTitleVisible: false,
        }}
      />
      <Stack.Screen
        name="Settings"
        component={SettingsScreen}
        options={{
          title: 'Settings',
          headerBackTitleVisible: false,
        }}
      />
    </Stack.Navigator>
  );
};

export default MainNavigator;
