export enum DealStatus {
  PENDING = 'PENDING',
  CLOSED = 'CLOSED',
  CANCELLED = 'CANCELLED',
  ON_HOLD = 'ON_HOLD',
  REJECTED = 'REJECTED',
}

export interface Deal {
  id: number;
  dealReference: string;
  agentId: number;
  agentName?: string;
  productId: number;
  productName?: string;
  productCode?: string;
  clientName: string;
  clientEmail?: string;
  clientPhone?: string;
  dealValue: number;
  quantity: number;
  dealDate: string;
  closeDate?: string;
  status: DealStatus;
  notes?: string;
  commissionCalculated: boolean;
  commissionPaid: boolean;
  paymentDate?: string;
  totalCommissionAmount?: number;
  createdAt: string;
  updatedAt: string;
}

export interface DealCreateRequest {
  productId: number;
  clientName: string;
  clientEmail?: string;
  clientPhone?: string;
  dealValue: number;
  quantity?: number;
  dealDate: string;
  notes?: string;
}

export interface DealUpdateRequest {
  productId?: number;
  clientName?: string;
  clientEmail?: string;
  clientPhone?: string;
  dealValue?: number;
  quantity?: number;
  dealDate?: string;
  status?: DealStatus;
  notes?: string;
}

export interface DealSummary {
  totalDeals: number;
  closedDeals: number;
  pendingDeals: number;
  cancelledDeals: number;
  totalValue: number;
  averageValue: number;
}

export interface DealFilters {
  status?: DealStatus[];
  productId?: number;
  dateFrom?: string;
  dateTo?: string;
  minValue?: number;
  maxValue?: number;
  clientName?: string;
}

export interface DealStatusBreakdown {
  status: string;
  count: number;
  percentage: number;
}

export interface MonthlyDealStats {
  month: string;
  dealCount: number;
  totalValue: number;
  averageValue: number;
}
