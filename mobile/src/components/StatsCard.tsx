import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Card, Title, Paragraph } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';

import { colors, spacing, shadows, borderRadius } from '../theme/theme';

interface StatsCardProps {
  title: string;
  value: string;
  icon: string;
  color: string;
  trend?: 'up' | 'down' | 'neutral';
  subtitle?: string;
  onPress?: () => void;
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  icon,
  color,
  trend = 'neutral',
  subtitle,
  onPress,
}) => {
  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return 'trending-up';
      case 'down':
        return 'trending-down';
      default:
        return 'trending-flat';
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case 'up':
        return colors.success;
      case 'down':
        return colors.error;
      default:
        return colors.textSecondary;
    }
  };

  return (
    <Card style={styles.card} onPress={onPress}>
      <LinearGradient
        colors={[colors.background, colors.backgroundLight]}
        style={styles.gradient}
      >
        <View style={styles.content}>
          <View style={styles.header}>
            <View style={[styles.iconContainer, { backgroundColor: color }]}>
              <Icon name={icon} size={24} color={colors.background} />
            </View>
            <View style={styles.trendContainer}>
              <Icon
                name={getTrendIcon()}
                size={16}
                color={getTrendColor()}
              />
            </View>
          </View>
          
          <View style={styles.body}>
            <Title style={styles.value} numberOfLines={1}>
              {value}
            </Title>
            <Paragraph style={styles.title} numberOfLines={1}>
              {title}
            </Paragraph>
            {subtitle && (
              <Paragraph style={styles.subtitle} numberOfLines={1}>
                {subtitle}
              </Paragraph>
            )}
          </View>
        </View>
      </LinearGradient>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    width: '48%',
    marginBottom: spacing.md,
    ...shadows.small,
    overflow: 'hidden',
  },
  gradient: {
    flex: 1,
  },
  content: {
    padding: spacing.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: borderRadius.lg,
    justifyContent: 'center',
    alignItems: 'center',
  },
  trendContainer: {
    padding: spacing.xs,
  },
  body: {
    flex: 1,
  },
  value: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  title: {
    fontSize: 14,
    color: colors.textSecondary,
    fontWeight: '500',
  },
  subtitle: {
    fontSize: 12,
    color: colors.textLight,
    marginTop: spacing.xs,
  },
});

export default StatsCard;
