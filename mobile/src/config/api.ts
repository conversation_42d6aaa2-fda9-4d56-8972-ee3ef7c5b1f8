import Config from 'react-native-config';

// API Configuration
export const API_BASE_URL = Config.API_BASE_URL || 'http://localhost:8080/api';

// API Endpoints
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    VERIFY: '/auth/verify',
    CHANGE_PASSWORD: '/auth/change-password',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password',
    GOOGLE_SIGNIN: '/auth/oauth/google',
  },
  
  // Users
  USERS: {
    BASE: '/users',
    PROFILE: '/users/profile',
    UPDATE_PROFILE: '/users/profile',
    SEARCH: '/users/search',
    BY_ROLE: '/users/by-role',
    STATISTICS: '/users/statistics',
  },
  
  // Deals
  DEALS: {
    BASE: '/deals',
    BY_AGENT: '/deals/by-agent',
    RECENT: '/deals/recent',
    STATISTICS: '/deals/statistics',
    SUMMARY: '/deals/summary',
    EXPORT: '/deals/export',
  },
  
  // Commissions
  COMMISSIONS: {
    BASE: '/commissions',
    BY_AGENT: '/commissions/by-agent',
    CALCULATE: '/commissions/calculate',
    APPROVE: '/commissions/approve',
    PAY: '/commissions/pay',
    STATISTICS: '/commissions/statistics',
    TRENDS: '/commissions/trends',
    EXPORT: '/commissions/export',
  },
  
  // Products
  PRODUCTS: {
    BASE: '/products',
    ACTIVE: '/products/active',
    CATEGORIES: '/products/categories',
    COMMISSION_RULES: '/products/commission-rules',
  },
  
  // Dashboard
  DASHBOARD: {
    BASE: '/dashboard',
    AGENT: '/dashboard/agent',
    MANAGER: '/dashboard/manager',
    ADMIN: '/dashboard/admin',
  },
  
  // Reports
  REPORTS: {
    BASE: '/reports',
    PERFORMANCE: '/reports/performance',
    COMMISSION: '/reports/commission',
    LEADERBOARD: '/reports/leaderboard',
    EXPORT: '/reports/export',
  },
  
  // Notifications
  NOTIFICATIONS: {
    BASE: '/notifications',
    UNREAD: '/notifications/unread',
    MARK_READ: '/notifications/mark-read',
    MARK_ALL_READ: '/notifications/mark-all-read',
  },
};

// Request timeout configuration
export const REQUEST_TIMEOUT = 30000; // 30 seconds

// Retry configuration
export const RETRY_CONFIG = {
  retries: 3,
  retryDelay: 1000, // 1 second
  retryCondition: (error: any) => {
    return error.code === 'NETWORK_ERROR' || 
           (error.response && error.response.status >= 500);
  },
};

// Cache configuration
export const CACHE_CONFIG = {
  defaultStaleTime: 5 * 60 * 1000, // 5 minutes
  defaultCacheTime: 10 * 60 * 1000, // 10 minutes
};

// File upload configuration
export const UPLOAD_CONFIG = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
  uploadEndpoint: '/upload',
};

// Pagination defaults
export const PAGINATION_DEFAULTS = {
  page: 0,
  size: 20,
  maxSize: 100,
};

// Date format configuration
export const DATE_FORMATS = {
  API: 'YYYY-MM-DDTHH:mm:ss',
  DISPLAY: 'MMM DD, YYYY',
  DISPLAY_WITH_TIME: 'MMM DD, YYYY HH:mm',
  INPUT: 'YYYY-MM-DD',
  MONTH_YEAR: 'YYYY-MM',
};

// Error codes
export const ERROR_CODES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
};

// Success messages
export const SUCCESS_MESSAGES = {
  LOGIN: 'Successfully logged in',
  LOGOUT: 'Successfully logged out',
  REGISTER: 'Account created successfully',
  PASSWORD_CHANGED: 'Password changed successfully',
  PASSWORD_RESET: 'Password reset email sent',
  DEAL_CREATED: 'Deal created successfully',
  DEAL_UPDATED: 'Deal updated successfully',
  DEAL_DELETED: 'Deal deleted successfully',
  PROFILE_UPDATED: 'Profile updated successfully',
  COMMISSION_CALCULATED: 'Commission calculated successfully',
  COMMISSION_APPROVED: 'Commission approved successfully',
  COMMISSION_PAID: 'Commission paid successfully',
};

// Error messages
export const ERROR_MESSAGES = {
  NETWORK: 'Network error. Please check your connection.',
  TIMEOUT: 'Request timeout. Please try again.',
  UNAUTHORIZED: 'Session expired. Please log in again.',
  FORBIDDEN: 'You do not have permission to perform this action.',
  NOT_FOUND: 'The requested resource was not found.',
  VALIDATION: 'Please check your input and try again.',
  SERVER: 'Server error. Please try again later.',
  UNKNOWN: 'An unexpected error occurred.',
  FILE_TOO_LARGE: 'File size exceeds the maximum limit.',
  INVALID_FILE_TYPE: 'Invalid file type.',
};
