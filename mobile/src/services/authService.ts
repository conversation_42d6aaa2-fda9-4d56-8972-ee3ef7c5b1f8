import axios, { AxiosResponse } from 'axios';
import { API_BASE_URL } from '../config/api';
import {
  AuthResponse,
  LoginRequest,
  RegisterRequest,
  ChangePasswordRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest,
} from '../types/user';

class AuthService {
  private baseURL = `${API_BASE_URL}/auth`;

  /**
   * <PERSON>gin user with email and password
   */
  async login(email: string, password: string): Promise<AuthResponse> {
    const request: LoginRequest = { email, password };
    
    try {
      const response: AxiosResponse<AuthResponse> = await axios.post(
        `${this.baseURL}/login`,
        request
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Register new user
   */
  async register(userData: RegisterRequest): Promise<AuthResponse> {
    try {
      const response: AxiosResponse<AuthResponse> = await axios.post(
        `${this.baseURL}/register`,
        userData
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Logout user
   */
  async logout(token: string): Promise<void> {
    try {
      await axios.post(
        `${this.baseURL}/logout`,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
    } catch (error) {
      // Don't throw error for logout, just log it
      console.warn('Logout error:', error);
    }
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(refreshToken: string): Promise<{ token: string; refreshToken: string }> {
    try {
      const response: AxiosResponse<{ token: string; refreshToken: string }> = await axios.post(
        `${this.baseURL}/refresh`,
        { refreshToken }
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Verify if token is still valid
   */
  async verifyToken(token: string): Promise<boolean> {
    try {
      await axios.get(`${this.baseURL}/verify`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Change user password
   */
  async changePassword(
    token: string,
    passwordData: ChangePasswordRequest
  ): Promise<void> {
    try {
      await axios.post(`${this.baseURL}/change-password`, passwordData, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Request password reset
   */
  async forgotPassword(email: string): Promise<void> {
    const request: ForgotPasswordRequest = { email };
    
    try {
      await axios.post(`${this.baseURL}/forgot-password`, request);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Reset password with token
   */
  async resetPassword(resetData: ResetPasswordRequest): Promise<void> {
    try {
      await axios.post(`${this.baseURL}/reset-password`, resetData);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * OAuth2 Google Sign In
   */
  async googleSignIn(idToken: string): Promise<AuthResponse> {
    try {
      const response: AxiosResponse<AuthResponse> = await axios.post(
        `${this.baseURL}/oauth/google`,
        { idToken }
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Handle API errors
   */
  private handleError(error: any): Error {
    if (error.response) {
      // Server responded with error status
      const message = error.response.data?.message || 'Authentication failed';
      const status = error.response.status;
      
      switch (status) {
        case 400:
          return new Error(message || 'Invalid request');
        case 401:
          return new Error('Invalid credentials');
        case 403:
          return new Error('Access denied');
        case 404:
          return new Error('Service not found');
        case 409:
          return new Error(message || 'User already exists');
        case 422:
          return new Error(message || 'Validation failed');
        case 429:
          return new Error('Too many requests. Please try again later.');
        case 500:
          return new Error('Server error. Please try again later.');
        default:
          return new Error(message || 'Authentication failed');
      }
    } else if (error.request) {
      // Network error
      return new Error('Network error. Please check your connection.');
    } else {
      // Other error
      return new Error(error.message || 'An unexpected error occurred');
    }
  }
}

export const authService = new AuthService();
