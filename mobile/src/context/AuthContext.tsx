import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { authService } from '../services/authService';
import { User } from '../types/user';

// Auth state interface
interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  token: string | null;
  refreshToken: string | null;
}

// Auth actions
type AuthAction =
  | { type: 'AUTH_LOADING' }
  | { type: 'AUTH_SUCCESS'; payload: { user: User; token: string; refreshToken: string } }
  | { type: 'AUTH_FAILURE' }
  | { type: 'LOGOUT' }
  | { type: 'UPDATE_USER'; payload: User }
  | { type: 'RESTORE_TOKEN'; payload: { token: string; refreshToken: string; user: User } };

// Initial state
const initialState: AuthState = {
  isAuthenticated: false,
  isLoading: true,
  user: null,
  token: null,
  refreshToken: null,
};

// Auth reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'AUTH_LOADING':
      return {
        ...state,
        isLoading: true,
      };
    case 'AUTH_SUCCESS':
      return {
        ...state,
        isAuthenticated: true,
        isLoading: false,
        user: action.payload.user,
        token: action.payload.token,
        refreshToken: action.payload.refreshToken,
      };
    case 'AUTH_FAILURE':
      return {
        ...state,
        isAuthenticated: false,
        isLoading: false,
        user: null,
        token: null,
        refreshToken: null,
      };
    case 'LOGOUT':
      return {
        ...initialState,
        isLoading: false,
      };
    case 'UPDATE_USER':
      return {
        ...state,
        user: action.payload,
      };
    case 'RESTORE_TOKEN':
      return {
        ...state,
        isAuthenticated: true,
        isLoading: false,
        token: action.payload.token,
        refreshToken: action.payload.refreshToken,
        user: action.payload.user,
      };
    default:
      return state;
  }
};

// Auth context interface
interface AuthContextType {
  state: AuthState;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  register: (userData: any) => Promise<void>;
  updateUser: (user: User) => void;
  refreshAuthToken: () => Promise<void>;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Storage keys
const STORAGE_KEYS = {
  TOKEN: '@quotasnap_token',
  REFRESH_TOKEN: '@quotasnap_refresh_token',
  USER: '@quotasnap_user',
};

// Auth provider component
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Restore authentication state on app start
  useEffect(() => {
    const restoreAuth = async () => {
      try {
        const token = await AsyncStorage.getItem(STORAGE_KEYS.TOKEN);
        const refreshToken = await AsyncStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
        const userString = await AsyncStorage.getItem(STORAGE_KEYS.USER);

        if (token && refreshToken && userString) {
          const user = JSON.parse(userString);
          
          // Verify token is still valid
          const isValid = await authService.verifyToken(token);
          
          if (isValid) {
            dispatch({
              type: 'RESTORE_TOKEN',
              payload: { token, refreshToken, user },
            });
          } else {
            // Try to refresh token
            try {
              const newTokens = await authService.refreshToken(refreshToken);
              dispatch({
                type: 'RESTORE_TOKEN',
                payload: {
                  token: newTokens.token,
                  refreshToken: newTokens.refreshToken,
                  user,
                },
              });
              
              // Store new tokens
              await AsyncStorage.setItem(STORAGE_KEYS.TOKEN, newTokens.token);
              await AsyncStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, newTokens.refreshToken);
            } catch (refreshError) {
              // Refresh failed, clear storage
              await clearAuthStorage();
              dispatch({ type: 'AUTH_FAILURE' });
            }
          }
        } else {
          dispatch({ type: 'AUTH_FAILURE' });
        }
      } catch (error) {
        console.error('Error restoring auth state:', error);
        dispatch({ type: 'AUTH_FAILURE' });
      }
    };

    restoreAuth();
  }, []);

  // Clear auth storage
  const clearAuthStorage = async () => {
    await AsyncStorage.multiRemove([
      STORAGE_KEYS.TOKEN,
      STORAGE_KEYS.REFRESH_TOKEN,
      STORAGE_KEYS.USER,
    ]);
  };

  // Login function
  const login = async (email: string, password: string) => {
    dispatch({ type: 'AUTH_LOADING' });
    
    try {
      const response = await authService.login(email, password);
      
      // Store tokens and user data
      await AsyncStorage.setItem(STORAGE_KEYS.TOKEN, response.token);
      await AsyncStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, response.refreshToken);
      await AsyncStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(response.user));
      
      dispatch({
        type: 'AUTH_SUCCESS',
        payload: {
          user: response.user,
          token: response.token,
          refreshToken: response.refreshToken,
        },
      });
    } catch (error) {
      dispatch({ type: 'AUTH_FAILURE' });
      throw error;
    }
  };

  // Logout function
  const logout = async () => {
    try {
      if (state.token) {
        await authService.logout(state.token);
      }
    } catch (error) {
      console.error('Error during logout:', error);
    } finally {
      await clearAuthStorage();
      dispatch({ type: 'LOGOUT' });
    }
  };

  // Register function
  const register = async (userData: any) => {
    dispatch({ type: 'AUTH_LOADING' });
    
    try {
      const response = await authService.register(userData);
      
      // Store tokens and user data
      await AsyncStorage.setItem(STORAGE_KEYS.TOKEN, response.token);
      await AsyncStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, response.refreshToken);
      await AsyncStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(response.user));
      
      dispatch({
        type: 'AUTH_SUCCESS',
        payload: {
          user: response.user,
          token: response.token,
          refreshToken: response.refreshToken,
        },
      });
    } catch (error) {
      dispatch({ type: 'AUTH_FAILURE' });
      throw error;
    }
  };

  // Update user function
  const updateUser = (user: User) => {
    dispatch({ type: 'UPDATE_USER', payload: user });
    AsyncStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(user));
  };

  // Refresh auth token
  const refreshAuthToken = async () => {
    if (!state.refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const newTokens = await authService.refreshToken(state.refreshToken);
      
      // Store new tokens
      await AsyncStorage.setItem(STORAGE_KEYS.TOKEN, newTokens.token);
      await AsyncStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, newTokens.refreshToken);
      
      dispatch({
        type: 'AUTH_SUCCESS',
        payload: {
          user: state.user!,
          token: newTokens.token,
          refreshToken: newTokens.refreshToken,
        },
      });
    } catch (error) {
      await logout();
      throw error;
    }
  };

  const value: AuthContextType = {
    state,
    login,
    logout,
    register,
    updateUser,
    refreshAuthToken,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
