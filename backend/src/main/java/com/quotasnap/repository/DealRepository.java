package com.quotasnap.repository;

import com.quotasnap.entity.Deal;
import com.quotasnap.entity.Product;
import com.quotasnap.entity.User;
import com.quotasnap.enums.DealStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Deal entity operations
 */
@Repository
public interface DealRepository extends JpaRepository<Deal, Long> {

    /**
     * Find deal by reference number
     */
    Optional<Deal> findByDealReference(String dealReference);

    /**
     * Check if deal reference exists
     */
    boolean existsByDealReference(String dealReference);

    /**
     * Find deals by agent
     */
    List<Deal> findByAgent(User agent);

    /**
     * Find deals by agent with pagination
     */
    Page<Deal> findByAgent(User agent, Pageable pageable);

    /**
     * Find deals by agent ID
     */
    @Query("SELECT d FROM Deal d WHERE d.agent.id = :agentId")
    List<Deal> findByAgentId(@Param("agentId") Long agentId);

    /**
     * Find deals by product
     */
    List<Deal> findByProduct(Product product);

    /**
     * Find deals by status
     */
    List<Deal> findByStatus(DealStatus status);

    /**
     * Find deals by agent and status
     */
    List<Deal> findByAgentAndStatus(User agent, DealStatus status);

    /**
     * Find deals within date range
     */
    List<Deal> findByDealDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find deals by agent within date range
     */
    List<Deal> findByAgentAndDealDateBetween(User agent, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find deals by agent and status within date range
     */
    List<Deal> findByAgentAndStatusAndDealDateBetween(User agent, DealStatus status, 
                                                      LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find deals with commission not calculated
     */
    @Query("SELECT d FROM Deal d WHERE d.status = 'CLOSED' AND d.commissionCalculated = false")
    List<Deal> findDealsWithoutCommission();

    /**
     * Find deals with commission calculated but not paid
     */
    @Query("SELECT d FROM Deal d WHERE d.commissionCalculated = true AND d.commissionPaid = false")
    List<Deal> findDealsWithUnpaidCommission();

    /**
     * Search deals by client name
     */
    @Query("SELECT d FROM Deal d WHERE LOWER(d.clientName) LIKE LOWER(CONCAT('%', :clientName, '%'))")
    Page<Deal> searchByClientName(@Param("clientName") String clientName, Pageable pageable);

    /**
     * Find deals by value range
     */
    List<Deal> findByDealValueBetween(BigDecimal minValue, BigDecimal maxValue);

    /**
     * Get total deal value by agent
     */
    @Query("SELECT COALESCE(SUM(d.dealValue), 0) FROM Deal d WHERE d.agent.id = :agentId AND d.status = 'CLOSED'")
    BigDecimal getTotalDealValueByAgent(@Param("agentId") Long agentId);

    /**
     * Get total deal value by agent within date range
     */
    @Query("SELECT COALESCE(SUM(d.dealValue), 0) FROM Deal d WHERE d.agent.id = :agentId " +
           "AND d.status = 'CLOSED' AND d.dealDate BETWEEN :startDate AND :endDate")
    BigDecimal getTotalDealValueByAgentAndDateRange(@Param("agentId") Long agentId,
                                                    @Param("startDate") LocalDateTime startDate,
                                                    @Param("endDate") LocalDateTime endDate);

    /**
     * Count deals by agent and status
     */
    long countByAgentAndStatus(User agent, DealStatus status);

    /**
     * Count deals by status within date range
     */
    @Query("SELECT COUNT(d) FROM Deal d WHERE d.status = :status AND d.dealDate BETWEEN :startDate AND :endDate")
    long countByStatusAndDateRange(@Param("status") DealStatus status,
                                   @Param("startDate") LocalDateTime startDate,
                                   @Param("endDate") LocalDateTime endDate);

    /**
     * Get monthly deal statistics
     */
    @Query("SELECT YEAR(d.dealDate) as year, MONTH(d.dealDate) as month, " +
           "COUNT(d) as dealCount, COALESCE(SUM(d.dealValue), 0) as totalValue " +
           "FROM Deal d WHERE d.status = 'CLOSED' " +
           "GROUP BY YEAR(d.dealDate), MONTH(d.dealDate) " +
           "ORDER BY year DESC, month DESC")
    List<Object[]> getMonthlyDealStatistics();

    /**
     * Get top performing agents by deal count
     */
    @Query("SELECT d.agent, COUNT(d) as dealCount, COALESCE(SUM(d.dealValue), 0) as totalValue " +
           "FROM Deal d WHERE d.status = 'CLOSED' " +
           "GROUP BY d.agent ORDER BY dealCount DESC")
    List<Object[]> getTopPerformingAgentsByDealCount(Pageable pageable);

    /**
     * Get top performing agents by deal value
     */
    @Query("SELECT d.agent, COUNT(d) as dealCount, COALESCE(SUM(d.dealValue), 0) as totalValue " +
           "FROM Deal d WHERE d.status = 'CLOSED' " +
           "GROUP BY d.agent ORDER BY totalValue DESC")
    List<Object[]> getTopPerformingAgentsByValue(Pageable pageable);

    /**
     * Find recent deals for dashboard
     */
    @Query("SELECT d FROM Deal d ORDER BY d.createdAt DESC")
    List<Deal> findRecentDeals(Pageable pageable);

    /**
     * Find deals by team (manager's subordinates)
     */
    @Query("SELECT d FROM Deal d WHERE d.agent.manager.id = :managerId")
    List<Deal> findDealsByTeam(@Param("managerId") Long managerId);
}
