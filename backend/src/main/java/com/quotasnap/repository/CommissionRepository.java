package com.quotasnap.repository;

import com.quotasnap.entity.Commission;
import com.quotasnap.entity.Deal;
import com.quotasnap.entity.User;
import com.quotasnap.enums.CommissionStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Repository interface for Commission entity operations
 */
@Repository
public interface CommissionRepository extends JpaRepository<Commission, Long> {

    /**
     * Find commissions by agent
     */
    List<Commission> findByAgent(User agent);

    /**
     * Find commissions by agent with pagination
     */
    Page<Commission> findByAgent(User agent, Pageable pageable);

    /**
     * Find commissions by agent ID
     */
    @Query("SELECT c FROM Commission c WHERE c.agent.id = :agentId")
    List<Commission> findByAgentId(@Param("agentId") Long agentId);

    /**
     * Find commissions by deal
     */
    List<Commission> findByDeal(Deal deal);

    /**
     * Find commissions by status
     */
    List<Commission> findByStatus(CommissionStatus status);

    /**
     * Find commissions by agent and status
     */
    List<Commission> findByAgentAndStatus(User agent, CommissionStatus status);

    /**
     * Find commissions by commission period
     */
    List<Commission> findByCommissionPeriod(String commissionPeriod);

    /**
     * Find commissions by agent and period
     */
    List<Commission> findByAgentAndCommissionPeriod(User agent, String commissionPeriod);

    /**
     * Find commissions within date range
     */
    List<Commission> findByCalculatedDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find paid commissions within date range
     */
    List<Commission> findByPaidDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find commissions by agent within date range
     */
    List<Commission> findByAgentAndCalculatedDateBetween(User agent, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Get total commission amount by agent
     */
    @Query("SELECT COALESCE(SUM(c.commissionAmount), 0) FROM Commission c WHERE c.agent.id = :agentId")
    BigDecimal getTotalCommissionByAgent(@Param("agentId") Long agentId);

    /**
     * Get total paid commission amount by agent
     */
    @Query("SELECT COALESCE(SUM(c.commissionAmount), 0) FROM Commission c " +
           "WHERE c.agent.id = :agentId AND c.status = 'PAID'")
    BigDecimal getTotalPaidCommissionByAgent(@Param("agentId") Long agentId);

    /**
     * Get total pending commission amount by agent
     */
    @Query("SELECT COALESCE(SUM(c.commissionAmount), 0) FROM Commission c " +
           "WHERE c.agent.id = :agentId AND c.status IN ('CALCULATED', 'APPROVED')")
    BigDecimal getTotalPendingCommissionByAgent(@Param("agentId") Long agentId);

    /**
     * Get commission amount by agent and period
     */
    @Query("SELECT COALESCE(SUM(c.commissionAmount), 0) FROM Commission c " +
           "WHERE c.agent.id = :agentId AND c.commissionPeriod = :period")
    BigDecimal getCommissionByAgentAndPeriod(@Param("agentId") Long agentId, @Param("period") String period);

    /**
     * Get commission amount by agent within date range
     */
    @Query("SELECT COALESCE(SUM(c.commissionAmount), 0) FROM Commission c " +
           "WHERE c.agent.id = :agentId AND c.calculatedDate BETWEEN :startDate AND :endDate")
    BigDecimal getCommissionByAgentAndDateRange(@Param("agentId") Long agentId,
                                                @Param("startDate") LocalDateTime startDate,
                                                @Param("endDate") LocalDateTime endDate);

    /**
     * Count commissions by agent and status
     */
    long countByAgentAndStatus(User agent, CommissionStatus status);

    /**
     * Get monthly commission statistics
     */
    @Query("SELECT c.commissionPeriod, COUNT(c) as commissionCount, " +
           "COALESCE(SUM(c.commissionAmount), 0) as totalAmount " +
           "FROM Commission c WHERE c.status = 'PAID' " +
           "GROUP BY c.commissionPeriod ORDER BY c.commissionPeriod DESC")
    List<Object[]> getMonthlyCommissionStatistics();

    /**
     * Get commission statistics by agent
     */
    @Query("SELECT c.agent, COUNT(c) as commissionCount, " +
           "COALESCE(SUM(c.commissionAmount), 0) as totalAmount, " +
           "COALESCE(AVG(c.commissionAmount), 0) as avgAmount " +
           "FROM Commission c WHERE c.status = 'PAID' " +
           "GROUP BY c.agent ORDER BY totalAmount DESC")
    List<Object[]> getCommissionStatisticsByAgent(Pageable pageable);

    /**
     * Find top earning agents
     */
    @Query("SELECT c.agent, COALESCE(SUM(c.commissionAmount), 0) as totalEarnings " +
           "FROM Commission c WHERE c.status = 'PAID' " +
           "GROUP BY c.agent ORDER BY totalEarnings DESC")
    List<Object[]> getTopEarningAgents(Pageable pageable);

    /**
     * Find commissions pending approval
     */
    @Query("SELECT c FROM Commission c WHERE c.status = 'CALCULATED' ORDER BY c.calculatedDate ASC")
    List<Commission> findCommissionsPendingApproval();

    /**
     * Find commissions approved for payment
     */
    @Query("SELECT c FROM Commission c WHERE c.status = 'APPROVED' ORDER BY c.calculatedDate ASC")
    List<Commission> findCommissionsApprovedForPayment();

    /**
     * Get commission trends by period
     */
    @Query("SELECT c.commissionPeriod, " +
           "COALESCE(SUM(CASE WHEN c.status = 'PAID' THEN c.commissionAmount ELSE 0 END), 0) as paidAmount, " +
           "COALESCE(SUM(CASE WHEN c.status IN ('CALCULATED', 'APPROVED') THEN c.commissionAmount ELSE 0 END), 0) as pendingAmount " +
           "FROM Commission c " +
           "GROUP BY c.commissionPeriod ORDER BY c.commissionPeriod DESC")
    List<Object[]> getCommissionTrendsByPeriod();

    /**
     * Find commissions by team (manager's subordinates)
     */
    @Query("SELECT c FROM Commission c WHERE c.agent.manager.id = :managerId")
    List<Commission> findCommissionsByTeam(@Param("managerId") Long managerId);

    /**
     * Get team commission summary
     */
    @Query("SELECT c.agent, COALESCE(SUM(c.commissionAmount), 0) as totalAmount " +
           "FROM Commission c WHERE c.agent.manager.id = :managerId AND c.status = 'PAID' " +
           "GROUP BY c.agent ORDER BY totalAmount DESC")
    List<Object[]> getTeamCommissionSummary(@Param("managerId") Long managerId);
}
