package com.quotasnap.repository;

import com.quotasnap.entity.User;
import com.quotasnap.enums.UserRole;
import com.quotasnap.enums.UserStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for User entity operations
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    /**
     * Find user by email address
     */
    Optional<User> findByEmail(String email);

    /**
     * Find user by employee ID
     */
    Optional<User> findByEmployeeId(String employeeId);

    /**
     * Find user by provider and provider ID (for OAuth2)
     */
    Optional<User> findByProviderAndProviderId(String provider, String providerId);

    /**
     * Check if email exists
     */
    boolean existsByEmail(String email);

    /**
     * Check if employee ID exists
     */
    boolean existsByEmployeeId(String employeeId);

    /**
     * Find users by role
     */
    List<User> findByRole(UserRole role);

    /**
     * Find users by status
     */
    List<User> findByStatus(UserStatus status);

    /**
     * Find active users by role
     */
    List<User> findByRoleAndStatus(UserRole role, UserStatus status);

    /**
     * Find users by manager
     */
    List<User> findByManager(User manager);

    /**
     * Find users by manager ID
     */
    @Query("SELECT u FROM User u WHERE u.manager.id = :managerId")
    List<User> findByManagerId(@Param("managerId") Long managerId);

    /**
     * Find all sales agents under a manager (including sub-managers)
     */
    @Query("SELECT u FROM User u WHERE u.role = 'SALES_AGENT' AND " +
           "(u.manager.id = :managerId OR u.manager.manager.id = :managerId)")
    List<User> findSalesAgentsByManagerHierarchy(@Param("managerId") Long managerId);

    /**
     * Find users by department
     */
    List<User> findByDepartment(String department);

    /**
     * Search users by name or email
     */
    @Query("SELECT u FROM User u WHERE " +
           "LOWER(CONCAT(u.firstName, ' ', u.lastName)) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(u.email) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    Page<User> searchByNameOrEmail(@Param("searchTerm") String searchTerm, Pageable pageable);

    /**
     * Find users hired within a date range
     */
    List<User> findByHireDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find users who haven't logged in since a specific date
     */
    @Query("SELECT u FROM User u WHERE u.lastLogin IS NULL OR u.lastLogin < :date")
    List<User> findUsersNotLoggedInSince(@Param("date") LocalDateTime date);

    /**
     * Count users by role
     */
    long countByRole(UserRole role);

    /**
     * Count active users
     */
    long countByStatus(UserStatus status);

    /**
     * Find top performing sales agents by commission count
     */
    @Query("SELECT u FROM User u JOIN Commission c ON u.id = c.agent.id " +
           "WHERE u.role = 'SALES_AGENT' AND c.status = 'PAID' " +
           "GROUP BY u.id ORDER BY COUNT(c.id) DESC")
    List<User> findTopPerformingAgents(Pageable pageable);

    /**
     * Update last login time
     */
    @Query("UPDATE User u SET u.lastLogin = :loginTime WHERE u.id = :userId")
    void updateLastLogin(@Param("userId") Long userId, @Param("loginTime") LocalDateTime loginTime);
}
