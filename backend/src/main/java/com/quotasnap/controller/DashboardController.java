package com.quotasnap.controller;

import com.quotasnap.dto.DashboardDataDto;
import com.quotasnap.service.DashboardService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

/**
 * REST controller for dashboard operations
 */
@RestController
@RequestMapping("/dashboard")
@Tag(name = "Dashboard", description = "Dashboard data and analytics endpoints")
@CrossOrigin(origins = "*", maxAge = 3600)
public class DashboardController {

    private final DashboardService dashboardService;

    @Autowired
    public DashboardController(DashboardService dashboardService) {
        this.dashboardService = dashboardService;
    }

    /**
     * Get agent dashboard data
     */
    @GetMapping("/agent")
    @Operation(summary = "Get agent dashboard", description = "Get dashboard data for sales agent")
    public ResponseEntity<DashboardDataDto> getAgentDashboard(
            Authentication authentication,
            @RequestParam(required = false) String period) {
        
        DashboardDataDto dashboardData = dashboardService.getAgentDashboard(
            authentication.getName(), period);
        return ResponseEntity.ok(dashboardData);
    }

    /**
     * Get manager dashboard data
     */
    @GetMapping("/manager")
    @Operation(summary = "Get manager dashboard", description = "Get dashboard data for manager")
    public ResponseEntity<DashboardDataDto> getManagerDashboard(
            Authentication authentication,
            @RequestParam(required = false) String period) {
        
        DashboardDataDto dashboardData = dashboardService.getManagerDashboard(
            authentication.getName(), period);
        return ResponseEntity.ok(dashboardData);
    }

    /**
     * Get admin dashboard data
     */
    @GetMapping("/admin")
    @Operation(summary = "Get admin dashboard", description = "Get dashboard data for admin")
    public ResponseEntity<DashboardDataDto> getAdminDashboard(
            @RequestParam(required = false) String period) {
        
        DashboardDataDto dashboardData = dashboardService.getAdminDashboard(period);
        return ResponseEntity.ok(dashboardData);
    }

    /**
     * Get dashboard data for specific agent (manager/admin only)
     */
    @GetMapping("/agent/{agentId}")
    @Operation(summary = "Get specific agent dashboard", description = "Get dashboard data for specific agent")
    public ResponseEntity<DashboardDataDto> getAgentDashboardById(
            @PathVariable Long agentId,
            @RequestParam(required = false) String period,
            Authentication authentication) {
        
        DashboardDataDto dashboardData = dashboardService.getAgentDashboardById(
            agentId, period, authentication.getName());
        return ResponseEntity.ok(dashboardData);
    }

    /**
     * Get team dashboard data (manager only)
     */
    @GetMapping("/team")
    @Operation(summary = "Get team dashboard", description = "Get dashboard data for manager's team")
    public ResponseEntity<DashboardDataDto> getTeamDashboard(
            Authentication authentication,
            @RequestParam(required = false) String period) {
        
        DashboardDataDto dashboardData = dashboardService.getTeamDashboard(
            authentication.getName(), period);
        return ResponseEntity.ok(dashboardData);
    }

    /**
     * Get commission trends
     */
    @GetMapping("/commission-trends")
    @Operation(summary = "Get commission trends", description = "Get commission trends data")
    public ResponseEntity<Object> getCommissionTrends(
            Authentication authentication,
            @RequestParam(required = false) String period,
            @RequestParam(required = false) Long agentId) {
        
        Object trends = dashboardService.getCommissionTrends(
            authentication.getName(), period, agentId);
        return ResponseEntity.ok(trends);
    }

    /**
     * Get deal status breakdown
     */
    @GetMapping("/deal-status-breakdown")
    @Operation(summary = "Get deal status breakdown", description = "Get deal status breakdown data")
    public ResponseEntity<Object> getDealStatusBreakdown(
            Authentication authentication,
            @RequestParam(required = false) String period,
            @RequestParam(required = false) Long agentId) {
        
        Object breakdown = dashboardService.getDealStatusBreakdown(
            authentication.getName(), period, agentId);
        return ResponseEntity.ok(breakdown);
    }

    /**
     * Get performance metrics
     */
    @GetMapping("/performance-metrics")
    @Operation(summary = "Get performance metrics", description = "Get performance metrics data")
    public ResponseEntity<Object> getPerformanceMetrics(
            Authentication authentication,
            @RequestParam(required = false) String period,
            @RequestParam(required = false) Long agentId) {
        
        Object metrics = dashboardService.getPerformanceMetrics(
            authentication.getName(), period, agentId);
        return ResponseEntity.ok(metrics);
    }

    /**
     * Get recent activities
     */
    @GetMapping("/recent-activities")
    @Operation(summary = "Get recent activities", description = "Get recent activities data")
    public ResponseEntity<Object> getRecentActivities(
            Authentication authentication,
            @RequestParam(defaultValue = "10") int limit) {
        
        Object activities = dashboardService.getRecentActivities(
            authentication.getName(), limit);
        return ResponseEntity.ok(activities);
    }
}
