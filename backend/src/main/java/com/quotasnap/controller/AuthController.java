package com.quotasnap.controller;

import com.quotasnap.dto.AuthResponseDto;
import com.quotasnap.dto.LoginRequestDto;
import com.quotasnap.dto.RegisterRequestDto;
import com.quotasnap.dto.ChangePasswordRequestDto;
import com.quotasnap.dto.ForgotPasswordRequestDto;
import com.quotasnap.dto.ResetPasswordRequestDto;
import com.quotasnap.dto.RefreshTokenRequestDto;
import com.quotasnap.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

/**
 * REST controller for authentication operations
 */
@RestController
@RequestMapping("/auth")
@Tag(name = "Authentication", description = "Authentication and authorization endpoints")
@CrossOrigin(origins = "*", maxAge = 3600)
public class AuthController {

    private final AuthService authService;

    @Autowired
    public AuthController(AuthService authService) {
        this.authService = authService;
    }

    /**
     * User login endpoint
     */
    @PostMapping("/login")
    @Operation(summary = "User login", description = "Authenticate user with email and password")
    public ResponseEntity<AuthResponseDto> login(@Valid @RequestBody LoginRequestDto loginRequest) {
        AuthResponseDto response = authService.login(loginRequest);
        return ResponseEntity.ok(response);
    }

    /**
     * User registration endpoint
     */
    @PostMapping("/register")
    @Operation(summary = "User registration", description = "Register a new user account")
    public ResponseEntity<AuthResponseDto> register(@Valid @RequestBody RegisterRequestDto registerRequest) {
        AuthResponseDto response = authService.register(registerRequest);
        return ResponseEntity.ok(response);
    }

    /**
     * User logout endpoint
     */
    @PostMapping("/logout")
    @Operation(summary = "User logout", description = "Logout user and invalidate token")
    public ResponseEntity<Void> logout(Authentication authentication) {
        authService.logout(authentication.getName());
        return ResponseEntity.ok().build();
    }

    /**
     * Refresh token endpoint
     */
    @PostMapping("/refresh")
    @Operation(summary = "Refresh token", description = "Refresh authentication token")
    public ResponseEntity<AuthResponseDto> refreshToken(@Valid @RequestBody RefreshTokenRequestDto refreshRequest) {
        AuthResponseDto response = authService.refreshToken(refreshRequest.getRefreshToken());
        return ResponseEntity.ok(response);
    }

    /**
     * Verify token endpoint
     */
    @GetMapping("/verify")
    @Operation(summary = "Verify token", description = "Verify if the current token is valid")
    public ResponseEntity<Void> verifyToken(Authentication authentication) {
        // If we reach here, the token is valid (handled by security filter)
        return ResponseEntity.ok().build();
    }

    /**
     * Change password endpoint
     */
    @PostMapping("/change-password")
    @Operation(summary = "Change password", description = "Change user password")
    public ResponseEntity<Void> changePassword(
            @Valid @RequestBody ChangePasswordRequestDto changePasswordRequest,
            Authentication authentication) {
        authService.changePassword(authentication.getName(), changePasswordRequest);
        return ResponseEntity.ok().build();
    }

    /**
     * Forgot password endpoint
     */
    @PostMapping("/forgot-password")
    @Operation(summary = "Forgot password", description = "Request password reset email")
    public ResponseEntity<Void> forgotPassword(@Valid @RequestBody ForgotPasswordRequestDto forgotPasswordRequest) {
        authService.forgotPassword(forgotPasswordRequest.getEmail());
        return ResponseEntity.ok().build();
    }

    /**
     * Reset password endpoint
     */
    @PostMapping("/reset-password")
    @Operation(summary = "Reset password", description = "Reset password with token")
    public ResponseEntity<Void> resetPassword(@Valid @RequestBody ResetPasswordRequestDto resetPasswordRequest) {
        authService.resetPassword(resetPasswordRequest);
        return ResponseEntity.ok().build();
    }

    /**
     * Google OAuth2 sign-in endpoint
     */
    @PostMapping("/oauth/google")
    @Operation(summary = "Google OAuth2 sign-in", description = "Authenticate with Google OAuth2")
    public ResponseEntity<AuthResponseDto> googleSignIn(@RequestBody GoogleSignInRequestDto googleSignInRequest) {
        AuthResponseDto response = authService.googleSignIn(googleSignInRequest.getIdToken());
        return ResponseEntity.ok(response);
    }

    /**
     * Get current user info endpoint
     */
    @GetMapping("/me")
    @Operation(summary = "Get current user", description = "Get current authenticated user information")
    public ResponseEntity<UserDto> getCurrentUser(Authentication authentication) {
        UserDto user = authService.getCurrentUser(authentication.getName());
        return ResponseEntity.ok(user);
    }

    /**
     * DTO for Google sign-in request
     */
    public static class GoogleSignInRequestDto {
        private String idToken;

        public String getIdToken() {
            return idToken;
        }

        public void setIdToken(String idToken) {
            this.idToken = idToken;
        }
    }
}
