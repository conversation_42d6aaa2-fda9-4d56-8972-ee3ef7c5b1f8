package com.quotasnap.enums;

/**
 * Enumeration for product status in the QuotaSnap system
 */
public enum ProductStatus {
    ACTIVE("Active", "Product is available for sale"),
    INACTIVE("Inactive", "Product is temporarily unavailable"),
    DISCONTINUED("Discontinued", "Product is no longer available"),
    DRAFT("Draft", "Product is in development");

    private final String displayName;
    private final String description;

    ProductStatus(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    /**
     * Check if product can be sold with this status
     */
    public boolean canBeSold() {
        return this == ACTIVE;
    }

    /**
     * Check if product can be activated
     */
    public boolean canBeActivated() {
        return this == INACTIVE || this == DRAFT;
    }
}
