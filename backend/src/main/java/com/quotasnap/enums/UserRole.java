package com.quotasnap.enums;

/**
 * Enumeration for user roles in the QuotaSnap system
 */
public enum UserRole {
    SALES_AGENT("Sales Agent", "Can view personal commission data and input deals"),
    MANAGER("Manager", "Can view team performance and manage commission rules"),
    FINANCE("Finance", "Can access financial reports and export data"),
    ADMIN("Administrator", "Full system access and user management");

    private final String displayName;
    private final String description;

    UserRole(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    /**
     * Check if this role has administrative privileges
     */
    public boolean isAdmin() {
        return this == ADMIN;
    }

    /**
     * Check if this role can manage other users
     */
    public boolean canManageUsers() {
        return this == ADMIN || this == MANAGER;
    }

    /**
     * Check if this role can view financial data
     */
    public boolean canViewFinancialData() {
        return this == ADMIN || this == FINANCE || this == MANAGER;
    }

    /**
     * Check if this role can export reports
     */
    public boolean canExportReports() {
        return this == ADMIN || this == FINANCE || this == MANAGER;
    }
}
