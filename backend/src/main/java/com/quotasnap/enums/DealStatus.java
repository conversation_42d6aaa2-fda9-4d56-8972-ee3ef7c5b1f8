package com.quotasnap.enums;

/**
 * Enumeration for deal status in the QuotaSnap system
 */
public enum DealStatus {
    PENDING("Pending", "Deal is pending approval or processing"),
    CLOSED("Closed", "Deal has been successfully closed"),
    CANCELLED("Cancelled", "Deal has been cancelled"),
    ON_HOLD("On Hold", "Deal is temporarily on hold"),
    REJECTED("Rejected", "Deal has been rejected");

    private final String displayName;
    private final String description;

    DealStatus(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    /**
     * Check if deal is eligible for commission calculation
     */
    public boolean isCommissionEligible() {
        return this == CLOSED;
    }

    /**
     * Check if deal can be modified
     */
    public boolean canBeModified() {
        return this == PENDING || this == ON_HOLD;
    }

    /**
     * Check if deal is in final state
     */
    public boolean isFinalState() {
        return this == CLOSED || this == CANCELLED || this == REJECTED;
    }
}
