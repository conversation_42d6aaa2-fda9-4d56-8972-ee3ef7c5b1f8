package com.quotasnap.enums;

/**
 * Enumeration for commission status in the QuotaSnap system
 */
public enum CommissionStatus {
    CALCULATED("Calculated", "Commission has been calculated but not yet approved"),
    APPROVED("Approved", "Commission has been approved for payment"),
    PAID("Paid", "Commission has been paid to the agent"),
    REJECTED("Rejected", "Commission has been rejected"),
    ON_HOLD("On Hold", "Commission payment is on hold");

    private final String displayName;
    private final String description;

    CommissionStatus(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    /**
     * Check if commission can be paid
     */
    public boolean canBePaid() {
        return this == APPROVED;
    }

    /**
     * Check if commission can be approved
     */
    public boolean canBeApproved() {
        return this == CALCULATED || this == ON_HOLD;
    }

    /**
     * Check if commission is in final state
     */
    public boolean isFinalState() {
        return this == PAID || this == REJECTED;
    }

    /**
     * Check if commission is pending action
     */
    public boolean isPending() {
        return this == CALCULATED || this == APPROVED || this == ON_HOLD;
    }
}
