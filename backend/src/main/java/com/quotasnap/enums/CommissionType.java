package com.quotasnap.enums;

/**
 * Enumeration for commission calculation types
 */
public enum CommissionType {
    PERCENTAGE("Percentage", "Commission calculated as percentage of deal value"),
    FIXED("Fixed Amount", "Fixed commission amount regardless of deal value"),
    TIERED("Tiered", "Commission rate varies based on deal value tiers"),
    HYBRID("Hybrid", "Combination of fixed amount and percentage");

    private final String displayName;
    private final String description;

    CommissionType(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    /**
     * Check if this commission type requires a percentage rate
     */
    public boolean requiresPercentageRate() {
        return this == PERCENTAGE || this == TIERED || this == HYBRID;
    }

    /**
     * Check if this commission type requires a fixed amount
     */
    public boolean requiresFixedAmount() {
        return this == FIXED || this == HYBRID;
    }
}
