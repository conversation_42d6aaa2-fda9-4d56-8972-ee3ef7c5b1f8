package com.quotasnap.enums;

/**
 * Enumeration for user status in the QuotaSnap system
 */
public enum UserStatus {
    ACTIVE("Active", "User is active and can access the system"),
    INACTIVE("Inactive", "User is temporarily disabled"),
    SUSPENDED("Suspended", "User is suspended due to policy violation"),
    PENDING("Pending", "User registration is pending approval"),
    ARCHIVED("Archived", "User account is archived");

    private final String displayName;
    private final String description;

    UserStatus(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    /**
     * Check if user can login with this status
     */
    public boolean canLogin() {
        return this == ACTIVE;
    }

    /**
     * Check if user can be activated
     */
    public boolean canBeActivated() {
        return this == INACTIVE || this == PENDING;
    }
}
