package com.quotasnap.mapper;

import com.quotasnap.dto.UserCreateDto;
import com.quotasnap.dto.UserDto;
import com.quotasnap.dto.UserUpdateDto;
import com.quotasnap.entity.User;
import org.mapstruct.*;

import java.util.List;

/**
 * MapStruct mapper for User entity and DTOs
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface UserMapper {

    /**
     * Convert User entity to UserDto
     */
    @Mapping(source = "manager.id", target = "managerId")
    @Mapping(source = "manager.firstName", target = "managerName", 
             qualifiedByName = "getManagerFullName")
    UserDto toDto(User user);

    /**
     * Convert UserCreateDto to User entity
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "passwordHash", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "emailVerified", ignore = true)
    @Mapping(target = "hireDate", ignore = true)
    @Mapping(target = "lastLogin", ignore = true)
    @Mapping(target = "provider", ignore = true)
    @Mapping(target = "providerId", ignore = true)
    @Mapping(target = "manager", ignore = true)
    @Mapping(target = "subordinates", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "version", ignore = true)
    User toEntity(UserCreateDto createDto);

    /**
     * Update User entity from UserUpdateDto
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "passwordHash", ignore = true)
    @Mapping(target = "emailVerified", ignore = true)
    @Mapping(target = "hireDate", ignore = true)
    @Mapping(target = "lastLogin", ignore = true)
    @Mapping(target = "provider", ignore = true)
    @Mapping(target = "providerId", ignore = true)
    @Mapping(target = "manager", ignore = true)
    @Mapping(target = "subordinates", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "version", ignore = true)
    void updateEntityFromDto(UserUpdateDto updateDto, @MappingTarget User user);

    /**
     * Convert list of User entities to list of UserDtos
     */
    List<UserDto> toDtoList(List<User> users);

    /**
     * Get manager full name for mapping
     */
    @Named("getManagerFullName")
    default String getManagerFullName(String firstName) {
        // This will be handled by the main mapping, just return the firstName
        // The actual full name will be constructed in the mapping
        return firstName;
    }

    /**
     * After mapping method to set manager full name
     */
    @AfterMapping
    default void setManagerFullName(@MappingTarget UserDto userDto, User user) {
        if (user.getManager() != null) {
            userDto.setManagerName(user.getManager().getFullName());
        }
    }
}
