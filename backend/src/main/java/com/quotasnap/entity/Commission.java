package com.quotasnap.entity;

import com.quotasnap.enums.CommissionStatus;
import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Commission entity representing calculated commissions for deals
 */
@Entity
@Table(name = "commissions", indexes = {
    @Index(name = "idx_commission_agent", columnList = "agent_id"),
    @Index(name = "idx_commission_deal", columnList = "deal_id"),
    @Index(name = "idx_commission_status", columnList = "status"),
    @Index(name = "idx_commission_period", columnList = "commission_period"),
    @Index(name = "idx_commission_calculated_date", columnList = "calculated_date")
})
public class Commission extends BaseEntity {

    @NotNull(message = "Sales agent is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "agent_id", nullable = false)
    private User agent;

    @NotNull(message = "Deal is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "deal_id", nullable = false)
    private Deal deal;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "commission_rule_id")
    private CommissionRule commissionRule;

    @NotNull(message = "Commission amount is required")
    @DecimalMin(value = "0.0", message = "Commission amount must be non-negative")
    @Column(name = "commission_amount", nullable = false, precision = 15, scale = 2)
    private BigDecimal commissionAmount;

    @Column(name = "commission_rate", precision = 5, scale = 2)
    private BigDecimal commissionRate;

    @NotNull(message = "Calculated date is required")
    @Column(name = "calculated_date", nullable = false)
    private LocalDateTime calculatedDate;

    @Column(name = "paid_date")
    private LocalDateTime paidDate;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private CommissionStatus status = CommissionStatus.CALCULATED;

    @Column(name = "commission_period", nullable = false, length = 7)
    private String commissionPeriod; // Format: YYYY-MM

    @Column(name = "notes", length = 500)
    private String notes;

    @Column(name = "payment_reference", length = 100)
    private String paymentReference;

    // Constructors
    public Commission() {}

    public Commission(User agent, Deal deal, CommissionRule commissionRule, 
                     BigDecimal commissionAmount, String commissionPeriod) {
        this.agent = agent;
        this.deal = deal;
        this.commissionRule = commissionRule;
        this.commissionAmount = commissionAmount;
        this.commissionPeriod = commissionPeriod;
        this.calculatedDate = LocalDateTime.now();
        this.status = CommissionStatus.CALCULATED;
        
        if (commissionRule != null) {
            this.commissionRate = commissionRule.getCommissionRate();
        }
    }

    // Utility methods
    public boolean isPaid() {
        return status == CommissionStatus.PAID;
    }

    public boolean isPending() {
        return status == CommissionStatus.CALCULATED || status == CommissionStatus.APPROVED;
    }

    public boolean isApproved() {
        return status == CommissionStatus.APPROVED;
    }

    public void markAsPaid(String paymentReference) {
        this.status = CommissionStatus.PAID;
        this.paidDate = LocalDateTime.now();
        this.paymentReference = paymentReference;
    }

    public void markAsApproved() {
        this.status = CommissionStatus.APPROVED;
    }

    public void markAsRejected(String reason) {
        this.status = CommissionStatus.REJECTED;
        this.notes = reason;
    }

    public String getFormattedPeriod() {
        if (commissionPeriod == null || commissionPeriod.length() != 7) {
            return "Unknown";
        }
        String[] parts = commissionPeriod.split("-");
        if (parts.length == 2) {
            return parts[1] + "/" + parts[0]; // MM/YYYY format
        }
        return commissionPeriod;
    }

    // Getters and Setters
    public User getAgent() {
        return agent;
    }

    public void setAgent(User agent) {
        this.agent = agent;
    }

    public Deal getDeal() {
        return deal;
    }

    public void setDeal(Deal deal) {
        this.deal = deal;
    }

    public CommissionRule getCommissionRule() {
        return commissionRule;
    }

    public void setCommissionRule(CommissionRule commissionRule) {
        this.commissionRule = commissionRule;
    }

    public BigDecimal getCommissionAmount() {
        return commissionAmount;
    }

    public void setCommissionAmount(BigDecimal commissionAmount) {
        this.commissionAmount = commissionAmount;
    }

    public BigDecimal getCommissionRate() {
        return commissionRate;
    }

    public void setCommissionRate(BigDecimal commissionRate) {
        this.commissionRate = commissionRate;
    }

    public LocalDateTime getCalculatedDate() {
        return calculatedDate;
    }

    public void setCalculatedDate(LocalDateTime calculatedDate) {
        this.calculatedDate = calculatedDate;
    }

    public LocalDateTime getPaidDate() {
        return paidDate;
    }

    public void setPaidDate(LocalDateTime paidDate) {
        this.paidDate = paidDate;
    }

    public CommissionStatus getStatus() {
        return status;
    }

    public void setStatus(CommissionStatus status) {
        this.status = status;
    }

    public String getCommissionPeriod() {
        return commissionPeriod;
    }

    public void setCommissionPeriod(String commissionPeriod) {
        this.commissionPeriod = commissionPeriod;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getPaymentReference() {
        return paymentReference;
    }

    public void setPaymentReference(String paymentReference) {
        this.paymentReference = paymentReference;
    }
}
