package com.quotasnap.entity;

import com.quotasnap.enums.ProductStatus;
import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Set;

/**
 * Product entity representing products/services that can be sold
 */
@Entity
@Table(name = "products", indexes = {
    @Index(name = "idx_product_code", columnList = "product_code"),
    @Index(name = "idx_product_category", columnList = "category"),
    @Index(name = "idx_product_status", columnList = "status")
})
public class Product extends BaseEntity {

    @NotBlank(message = "Product name is required")
    @Size(max = 100, message = "Product name must not exceed 100 characters")
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @NotBlank(message = "Product code is required")
    @Size(max = 50, message = "Product code must not exceed 50 characters")
    @Column(name = "product_code", nullable = false, unique = true, length = 50)
    private String productCode;

    @Column(name = "description", length = 500)
    private String description;

    @NotBlank(message = "Category is required")
    @Size(max = 50, message = "Category must not exceed 50 characters")
    @Column(name = "category", nullable = false, length = 50)
    private String category;

    @NotNull(message = "Base price is required")
    @DecimalMin(value = "0.0", inclusive = false, message = "Base price must be greater than 0")
    @Column(name = "base_price", nullable = false, precision = 15, scale = 2)
    private BigDecimal basePrice;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private ProductStatus status = ProductStatus.ACTIVE;

    @Column(name = "is_commission_eligible", nullable = false)
    private Boolean isCommissionEligible = true;

    @Column(name = "min_deal_value", precision = 15, scale = 2)
    private BigDecimal minDealValue;

    @Column(name = "max_deal_value", precision = 15, scale = 2)
    private BigDecimal maxDealValue;

    // Commission rules for this product
    @OneToMany(mappedBy = "product", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<CommissionRule> commissionRules = new HashSet<>();

    // Deals for this product
    @OneToMany(mappedBy = "product", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<Deal> deals = new HashSet<>();

    // Constructors
    public Product() {}

    public Product(String name, String productCode, String category, BigDecimal basePrice) {
        this.name = name;
        this.productCode = productCode;
        this.category = category;
        this.basePrice = basePrice;
        this.status = ProductStatus.ACTIVE;
        this.isCommissionEligible = true;
    }

    // Utility methods
    public boolean isActive() {
        return status == ProductStatus.ACTIVE;
    }

    public boolean isEligibleForCommission() {
        return isCommissionEligible && isActive();
    }

    public boolean isValidDealValue(BigDecimal dealValue) {
        if (dealValue == null) return false;
        
        boolean aboveMin = minDealValue == null || dealValue.compareTo(minDealValue) >= 0;
        boolean belowMax = maxDealValue == null || dealValue.compareTo(maxDealValue) <= 0;
        
        return aboveMin && belowMax;
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public BigDecimal getBasePrice() {
        return basePrice;
    }

    public void setBasePrice(BigDecimal basePrice) {
        this.basePrice = basePrice;
    }

    public ProductStatus getStatus() {
        return status;
    }

    public void setStatus(ProductStatus status) {
        this.status = status;
    }

    public Boolean getIsCommissionEligible() {
        return isCommissionEligible;
    }

    public void setIsCommissionEligible(Boolean isCommissionEligible) {
        this.isCommissionEligible = isCommissionEligible;
    }

    public BigDecimal getMinDealValue() {
        return minDealValue;
    }

    public void setMinDealValue(BigDecimal minDealValue) {
        this.minDealValue = minDealValue;
    }

    public BigDecimal getMaxDealValue() {
        return maxDealValue;
    }

    public void setMaxDealValue(BigDecimal maxDealValue) {
        this.maxDealValue = maxDealValue;
    }

    public Set<CommissionRule> getCommissionRules() {
        return commissionRules;
    }

    public void setCommissionRules(Set<CommissionRule> commissionRules) {
        this.commissionRules = commissionRules;
    }

    public Set<Deal> getDeals() {
        return deals;
    }

    public void setDeals(Set<Deal> deals) {
        this.deals = deals;
    }
}
