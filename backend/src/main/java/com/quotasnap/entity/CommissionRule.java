package com.quotasnap.entity;

import com.quotasnap.enums.CommissionType;
import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Commission rule entity defining how commissions are calculated for products
 */
@Entity
@Table(name = "commission_rules", indexes = {
    @Index(name = "idx_commission_rule_product", columnList = "product_id"),
    @Index(name = "idx_commission_rule_tier", columnList = "tier_level"),
    @Index(name = "idx_commission_rule_active", columnList = "is_active")
})
public class CommissionRule extends BaseEntity {

    @NotNull(message = "Product is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "product_id", nullable = false)
    private Product product;

    @NotNull(message = "Commission type is required")
    @Enumerated(EnumType.STRING)
    @Column(name = "commission_type", nullable = false, length = 20)
    private CommissionType commissionType;

    @NotNull(message = "Commission rate is required")
    @DecimalMin(value = "0.0", message = "Commission rate must be non-negative")
    @DecimalMax(value = "100.0", message = "Commission rate cannot exceed 100%")
    @Column(name = "commission_rate", nullable = false, precision = 5, scale = 2)
    private BigDecimal commissionRate;

    @Column(name = "fixed_amount", precision = 15, scale = 2)
    private BigDecimal fixedAmount;

    @Column(name = "tier_level", nullable = false)
    private Integer tierLevel = 1;

    @Column(name = "min_deal_value", precision = 15, scale = 2)
    private BigDecimal minDealValue;

    @Column(name = "max_deal_value", precision = 15, scale = 2)
    private BigDecimal maxDealValue;

    @Column(name = "min_quantity")
    private Integer minQuantity;

    @Column(name = "max_quantity")
    private Integer maxQuantity;

    @Column(name = "effective_from", nullable = false)
    private LocalDateTime effectiveFrom;

    @Column(name = "effective_to")
    private LocalDateTime effectiveTo;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @Column(name = "description", length = 500)
    private String description;

    // Constructors
    public CommissionRule() {}

    public CommissionRule(Product product, CommissionType commissionType, BigDecimal commissionRate, Integer tierLevel) {
        this.product = product;
        this.commissionType = commissionType;
        this.commissionRate = commissionRate;
        this.tierLevel = tierLevel;
        this.effectiveFrom = LocalDateTime.now();
        this.isActive = true;
    }

    // Utility methods
    public boolean isCurrentlyActive() {
        if (!isActive) return false;
        
        LocalDateTime now = LocalDateTime.now();
        boolean afterStart = effectiveFrom == null || now.isAfter(effectiveFrom) || now.isEqual(effectiveFrom);
        boolean beforeEnd = effectiveTo == null || now.isBefore(effectiveTo);
        
        return afterStart && beforeEnd;
    }

    public boolean isApplicableForDeal(BigDecimal dealValue, Integer quantity) {
        if (!isCurrentlyActive()) return false;
        
        // Check deal value range
        if (minDealValue != null && dealValue.compareTo(minDealValue) < 0) return false;
        if (maxDealValue != null && dealValue.compareTo(maxDealValue) > 0) return false;
        
        // Check quantity range
        if (quantity != null) {
            if (minQuantity != null && quantity < minQuantity) return false;
            if (maxQuantity != null && quantity > maxQuantity) return false;
        }
        
        return true;
    }

    public BigDecimal calculateCommission(BigDecimal dealValue, Integer quantity) {
        if (!isApplicableForDeal(dealValue, quantity)) {
            return BigDecimal.ZERO;
        }
        
        switch (commissionType) {
            case PERCENTAGE:
                return dealValue.multiply(commissionRate).divide(BigDecimal.valueOf(100));
            case FIXED:
                return fixedAmount != null ? fixedAmount : BigDecimal.ZERO;
            case TIERED:
                // For tiered commission, the rate applies to the entire deal value
                return dealValue.multiply(commissionRate).divide(BigDecimal.valueOf(100));
            default:
                return BigDecimal.ZERO;
        }
    }

    // Getters and Setters
    public Product getProduct() {
        return product;
    }

    public void setProduct(Product product) {
        this.product = product;
    }

    public CommissionType getCommissionType() {
        return commissionType;
    }

    public void setCommissionType(CommissionType commissionType) {
        this.commissionType = commissionType;
    }

    public BigDecimal getCommissionRate() {
        return commissionRate;
    }

    public void setCommissionRate(BigDecimal commissionRate) {
        this.commissionRate = commissionRate;
    }

    public BigDecimal getFixedAmount() {
        return fixedAmount;
    }

    public void setFixedAmount(BigDecimal fixedAmount) {
        this.fixedAmount = fixedAmount;
    }

    public Integer getTierLevel() {
        return tierLevel;
    }

    public void setTierLevel(Integer tierLevel) {
        this.tierLevel = tierLevel;
    }

    public BigDecimal getMinDealValue() {
        return minDealValue;
    }

    public void setMinDealValue(BigDecimal minDealValue) {
        this.minDealValue = minDealValue;
    }

    public BigDecimal getMaxDealValue() {
        return maxDealValue;
    }

    public void setMaxDealValue(BigDecimal maxDealValue) {
        this.maxDealValue = maxDealValue;
    }

    public Integer getMinQuantity() {
        return minQuantity;
    }

    public void setMinQuantity(Integer minQuantity) {
        this.minQuantity = minQuantity;
    }

    public Integer getMaxQuantity() {
        return maxQuantity;
    }

    public void setMaxQuantity(Integer maxQuantity) {
        this.maxQuantity = maxQuantity;
    }

    public LocalDateTime getEffectiveFrom() {
        return effectiveFrom;
    }

    public void setEffectiveFrom(LocalDateTime effectiveFrom) {
        this.effectiveFrom = effectiveFrom;
    }

    public LocalDateTime getEffectiveTo() {
        return effectiveTo;
    }

    public void setEffectiveTo(LocalDateTime effectiveTo) {
        this.effectiveTo = effectiveTo;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
