package com.quotasnap.entity;

import com.quotasnap.enums.DealStatus;
import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * Deal entity representing sales transactions
 */
@Entity
@Table(name = "deals", indexes = {
    @Index(name = "idx_deal_agent", columnList = "agent_id"),
    @Index(name = "idx_deal_product", columnList = "product_id"),
    @Index(name = "idx_deal_status", columnList = "status"),
    @Index(name = "idx_deal_date", columnList = "deal_date"),
    @Index(name = "idx_deal_reference", columnList = "deal_reference")
})
public class Deal extends BaseEntity {

    @NotBlank(message = "Deal reference is required")
    @Size(max = 100, message = "Deal reference must not exceed 100 characters")
    @Column(name = "deal_reference", nullable = false, unique = true, length = 100)
    private String dealReference;

    @NotNull(message = "Sales agent is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "agent_id", nullable = false)
    private User agent;

    @NotNull(message = "Product is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "product_id", nullable = false)
    private Product product;

    @NotBlank(message = "Client name is required")
    @Size(max = 200, message = "Client name must not exceed 200 characters")
    @Column(name = "client_name", nullable = false, length = 200)
    private String clientName;

    @Column(name = "client_email", length = 100)
    private String clientEmail;

    @Column(name = "client_phone", length = 20)
    private String clientPhone;

    @NotNull(message = "Deal value is required")
    @DecimalMin(value = "0.0", inclusive = false, message = "Deal value must be greater than 0")
    @Column(name = "deal_value", nullable = false, precision = 15, scale = 2)
    private BigDecimal dealValue;

    @Column(name = "quantity", nullable = false)
    private Integer quantity = 1;

    @NotNull(message = "Deal date is required")
    @Column(name = "deal_date", nullable = false)
    private LocalDateTime dealDate;

    @Column(name = "close_date")
    private LocalDateTime closeDate;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private DealStatus status = DealStatus.PENDING;

    @Column(name = "notes", length = 1000)
    private String notes;

    @Column(name = "commission_calculated", nullable = false)
    private Boolean commissionCalculated = false;

    @Column(name = "commission_paid", nullable = false)
    private Boolean commissionPaid = false;

    @Column(name = "payment_date")
    private LocalDateTime paymentDate;

    // Commission records for this deal
    @OneToMany(mappedBy = "deal", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<Commission> commissions = new HashSet<>();

    // Constructors
    public Deal() {}

    public Deal(String dealReference, User agent, Product product, String clientName, 
                BigDecimal dealValue, LocalDateTime dealDate) {
        this.dealReference = dealReference;
        this.agent = agent;
        this.product = product;
        this.clientName = clientName;
        this.dealValue = dealValue;
        this.dealDate = dealDate;
        this.quantity = 1;
        this.status = DealStatus.PENDING;
        this.commissionCalculated = false;
        this.commissionPaid = false;
    }

    // Utility methods
    public boolean isClosed() {
        return status == DealStatus.CLOSED;
    }

    public boolean isPending() {
        return status == DealStatus.PENDING;
    }

    public boolean isCancelled() {
        return status == DealStatus.CANCELLED;
    }

    public boolean isCommissionEligible() {
        return isClosed() && product != null && product.isEligibleForCommission();
    }

    public BigDecimal getTotalCommissionAmount() {
        return commissions.stream()
                .map(Commission::getCommissionAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public void markAsClosed() {
        this.status = DealStatus.CLOSED;
        this.closeDate = LocalDateTime.now();
    }

    public void markAsCancelled() {
        this.status = DealStatus.CANCELLED;
    }

    // Getters and Setters
    public String getDealReference() {
        return dealReference;
    }

    public void setDealReference(String dealReference) {
        this.dealReference = dealReference;
    }

    public User getAgent() {
        return agent;
    }

    public void setAgent(User agent) {
        this.agent = agent;
    }

    public Product getProduct() {
        return product;
    }

    public void setProduct(Product product) {
        this.product = product;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getClientEmail() {
        return clientEmail;
    }

    public void setClientEmail(String clientEmail) {
        this.clientEmail = clientEmail;
    }

    public String getClientPhone() {
        return clientPhone;
    }

    public void setClientPhone(String clientPhone) {
        this.clientPhone = clientPhone;
    }

    public BigDecimal getDealValue() {
        return dealValue;
    }

    public void setDealValue(BigDecimal dealValue) {
        this.dealValue = dealValue;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public LocalDateTime getDealDate() {
        return dealDate;
    }

    public void setDealDate(LocalDateTime dealDate) {
        this.dealDate = dealDate;
    }

    public LocalDateTime getCloseDate() {
        return closeDate;
    }

    public void setCloseDate(LocalDateTime closeDate) {
        this.closeDate = closeDate;
    }

    public DealStatus getStatus() {
        return status;
    }

    public void setStatus(DealStatus status) {
        this.status = status;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public Boolean getCommissionCalculated() {
        return commissionCalculated;
    }

    public void setCommissionCalculated(Boolean commissionCalculated) {
        this.commissionCalculated = commissionCalculated;
    }

    public Boolean getCommissionPaid() {
        return commissionPaid;
    }

    public void setCommissionPaid(Boolean commissionPaid) {
        this.commissionPaid = commissionPaid;
    }

    public LocalDateTime getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(LocalDateTime paymentDate) {
        this.paymentDate = paymentDate;
    }

    public Set<Commission> getCommissions() {
        return commissions;
    }

    public void setCommissions(Set<Commission> commissions) {
        this.commissions = commissions;
    }
}
