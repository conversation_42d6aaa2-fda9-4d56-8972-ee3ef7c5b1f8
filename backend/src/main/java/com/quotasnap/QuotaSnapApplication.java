package com.quotasnap;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Main application class for QuotaSnap Sales Commission Tracking System
 * 
 * This application provides a comprehensive platform for tracking sales commissions
 * across multiple products, clients, and tiers with real-time analytics.
 * 
 * Features:
 * - Real-time commission tracking
 * - Tiered commission rules
 * - Performance analytics and leaderboards
 * - Export capabilities (Excel/PDF)
 * - Multi-user role support
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableJpaAuditing
@EnableAsync
@EnableTransactionManagement
public class QuotaSnapApplication {

    public static void main(String[] args) {
        SpringApplication.run(QuotaSnapApplication.class, args);
    }
}
