package com.quotasnap.service;

import com.quotasnap.dto.UserCreateDto;
import com.quotasnap.dto.UserDto;
import com.quotasnap.dto.UserUpdateDto;
import com.quotasnap.entity.User;
import com.quotasnap.enums.UserRole;
import com.quotasnap.enums.UserStatus;
import com.quotasnap.exception.ResourceNotFoundException;
import com.quotasnap.exception.DuplicateResourceException;
import com.quotasnap.mapper.UserMapper;
import com.quotasnap.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service class for User entity operations
 */
@Service
@Transactional
public class UserService {

    private final UserRepository userRepository;
    private final UserMapper userMapper;
    private final PasswordEncoder passwordEncoder;

    @Autowired
    public UserService(UserRepository userRepository, UserMapper userMapper, PasswordEncoder passwordEncoder) {
        this.userRepository = userRepository;
        this.userMapper = userMapper;
        this.passwordEncoder = passwordEncoder;
    }

    /**
     * Create a new user
     */
    public UserDto createUser(UserCreateDto createDto) {
        // Check if email already exists
        if (userRepository.existsByEmail(createDto.getEmail())) {
            throw new DuplicateResourceException("Email already exists: " + createDto.getEmail());
        }

        // Check if employee ID already exists (if provided)
        if (createDto.getEmployeeId() != null && userRepository.existsByEmployeeId(createDto.getEmployeeId())) {
            throw new DuplicateResourceException("Employee ID already exists: " + createDto.getEmployeeId());
        }

        User user = userMapper.toEntity(createDto);
        
        // Encode password if provided
        if (createDto.getPassword() != null) {
            user.setPasswordHash(passwordEncoder.encode(createDto.getPassword()));
        }

        // Set default values
        user.setStatus(UserStatus.ACTIVE);
        user.setEmailVerified(false);
        user.setHireDate(LocalDateTime.now());

        User savedUser = userRepository.save(user);
        return userMapper.toDto(savedUser);
    }

    /**
     * Update an existing user
     */
    public UserDto updateUser(Long userId, UserUpdateDto updateDto) {
        User user = getUserById(userId);

        // Check email uniqueness if changed
        if (updateDto.getEmail() != null && !updateDto.getEmail().equals(user.getEmail())) {
            if (userRepository.existsByEmail(updateDto.getEmail())) {
                throw new DuplicateResourceException("Email already exists: " + updateDto.getEmail());
            }
        }

        // Check employee ID uniqueness if changed
        if (updateDto.getEmployeeId() != null && !updateDto.getEmployeeId().equals(user.getEmployeeId())) {
            if (userRepository.existsByEmployeeId(updateDto.getEmployeeId())) {
                throw new DuplicateResourceException("Employee ID already exists: " + updateDto.getEmployeeId());
            }
        }

        userMapper.updateEntityFromDto(updateDto, user);
        User savedUser = userRepository.save(user);
        return userMapper.toDto(savedUser);
    }

    /**
     * Get user by ID
     */
    @Transactional(readOnly = true)
    public UserDto getUserDtoById(Long userId) {
        User user = getUserById(userId);
        return userMapper.toDto(user);
    }

    /**
     * Get user entity by ID
     */
    @Transactional(readOnly = true)
    public User getUserById(Long userId) {
        return userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + userId));
    }

    /**
     * Get user by email
     */
    @Transactional(readOnly = true)
    public Optional<User> getUserByEmail(String email) {
        return userRepository.findByEmail(email);
    }

    /**
     * Get all users with pagination
     */
    @Transactional(readOnly = true)
    public Page<UserDto> getAllUsers(Pageable pageable) {
        Page<User> users = userRepository.findAll(pageable);
        return users.map(userMapper::toDto);
    }

    /**
     * Get users by role
     */
    @Transactional(readOnly = true)
    public List<UserDto> getUsersByRole(UserRole role) {
        List<User> users = userRepository.findByRole(role);
        return userMapper.toDtoList(users);
    }

    /**
     * Get active sales agents
     */
    @Transactional(readOnly = true)
    public List<UserDto> getActiveSalesAgents() {
        List<User> agents = userRepository.findByRoleAndStatus(UserRole.SALES_AGENT, UserStatus.ACTIVE);
        return userMapper.toDtoList(agents);
    }

    /**
     * Get users by manager
     */
    @Transactional(readOnly = true)
    public List<UserDto> getUsersByManager(Long managerId) {
        List<User> users = userRepository.findByManagerId(managerId);
        return userMapper.toDtoList(users);
    }

    /**
     * Search users by name or email
     */
    @Transactional(readOnly = true)
    public Page<UserDto> searchUsers(String searchTerm, Pageable pageable) {
        Page<User> users = userRepository.searchByNameOrEmail(searchTerm, pageable);
        return users.map(userMapper::toDto);
    }

    /**
     * Activate user
     */
    public UserDto activateUser(Long userId) {
        User user = getUserById(userId);
        user.setStatus(UserStatus.ACTIVE);
        User savedUser = userRepository.save(user);
        return userMapper.toDto(savedUser);
    }

    /**
     * Deactivate user
     */
    public UserDto deactivateUser(Long userId) {
        User user = getUserById(userId);
        user.setStatus(UserStatus.INACTIVE);
        User savedUser = userRepository.save(user);
        return userMapper.toDto(savedUser);
    }

    /**
     * Delete user (soft delete by setting status to ARCHIVED)
     */
    public void deleteUser(Long userId) {
        User user = getUserById(userId);
        user.setStatus(UserStatus.ARCHIVED);
        userRepository.save(user);
    }

    /**
     * Update last login time
     */
    public void updateLastLogin(Long userId) {
        userRepository.updateLastLogin(userId, LocalDateTime.now());
    }

    /**
     * Change user password
     */
    public void changePassword(Long userId, String newPassword) {
        User user = getUserById(userId);
        user.setPasswordHash(passwordEncoder.encode(newPassword));
        userRepository.save(user);
    }

    /**
     * Assign manager to user
     */
    public UserDto assignManager(Long userId, Long managerId) {
        User user = getUserById(userId);
        User manager = getUserById(managerId);
        
        // Validate manager role
        if (!manager.isManager()) {
            throw new IllegalArgumentException("Assigned user must have manager or admin role");
        }
        
        user.setManager(manager);
        User savedUser = userRepository.save(user);
        return userMapper.toDto(savedUser);
    }

    /**
     * Get user statistics
     */
    @Transactional(readOnly = true)
    public UserStatistics getUserStatistics() {
        long totalUsers = userRepository.count();
        long activeUsers = userRepository.countByStatus(UserStatus.ACTIVE);
        long salesAgents = userRepository.countByRole(UserRole.SALES_AGENT);
        long managers = userRepository.countByRole(UserRole.MANAGER);
        
        return new UserStatistics(totalUsers, activeUsers, salesAgents, managers);
    }

    /**
     * Inner class for user statistics
     */
    public static class UserStatistics {
        private final long totalUsers;
        private final long activeUsers;
        private final long salesAgents;
        private final long managers;

        public UserStatistics(long totalUsers, long activeUsers, long salesAgents, long managers) {
            this.totalUsers = totalUsers;
            this.activeUsers = activeUsers;
            this.salesAgents = salesAgents;
            this.managers = managers;
        }

        // Getters
        public long getTotalUsers() { return totalUsers; }
        public long getActiveUsers() { return activeUsers; }
        public long getSalesAgents() { return salesAgents; }
        public long getManagers() { return managers; }
    }
}
