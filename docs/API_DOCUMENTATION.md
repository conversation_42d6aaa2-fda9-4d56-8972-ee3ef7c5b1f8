# QuotaSnap API Documentation

## Overview

The QuotaSnap API is a RESTful web service built with Spring Boot that provides endpoints for managing sales commissions, deals, users, and analytics.

**Base URL:** `http://localhost:8080/api`

**Authentication:** JWT Bearer Token

## Authentication

### Login
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "user": {
    "id": 1,
    "firstName": "<PERSON>",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "role": "SALES_AGENT"
  },
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "refresh_token_here"
}
```

### Register
```http
POST /auth/register
Content-Type: application/json

{
  "firstName": "<PERSON>",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "SALES_AGENT"
}
```

### Refresh Token
```http
POST /auth/refresh
Content-Type: application/json

{
  "refreshToken": "refresh_token_here"
}
```

## Users

### Get Current User
```http
GET /auth/me
Authorization: Bearer {token}
```

### Get All Users
```http
GET /users?page=0&size=20
Authorization: Bearer {token}
```

### Get User by ID
```http
GET /users/{id}
Authorization: Bearer {token}
```

### Update User
```http
PUT /users/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "firstName": "John",
  "lastName": "Smith",
  "phoneNumber": "******-0123"
}
```

### Search Users
```http
GET /users/search?q=john&page=0&size=20
Authorization: Bearer {token}
```

## Deals

### Get Deals
```http
GET /deals?page=0&size=20&status=CLOSED
Authorization: Bearer {token}
```

### Get Deal by ID
```http
GET /deals/{id}
Authorization: Bearer {token}
```

### Create Deal
```http
POST /deals
Authorization: Bearer {token}
Content-Type: application/json

{
  "productId": 1,
  "clientName": "ABC Corporation",
  "clientEmail": "<EMAIL>",
  "dealValue": 15000.00,
  "quantity": 1,
  "dealDate": "2024-01-15T10:30:00",
  "notes": "Important client deal"
}
```

### Update Deal
```http
PUT /deals/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "status": "CLOSED",
  "closeDate": "2024-01-20T14:00:00",
  "notes": "Deal successfully closed"
}
```

### Delete Deal
```http
DELETE /deals/{id}
Authorization: Bearer {token}
```

### Get Agent Deals
```http
GET /deals/by-agent/{agentId}?page=0&size=20
Authorization: Bearer {token}
```

### Get Deal Statistics
```http
GET /deals/statistics?period=2024-01
Authorization: Bearer {token}
```

## Commissions

### Get Commissions
```http
GET /commissions?page=0&size=20&status=PAID
Authorization: Bearer {token}
```

### Get Commission by ID
```http
GET /commissions/{id}
Authorization: Bearer {token}
```

### Calculate Commission
```http
POST /commissions/calculate/{dealId}
Authorization: Bearer {token}
```

### Approve Commission
```http
POST /commissions/{id}/approve
Authorization: Bearer {token}
Content-Type: application/json

{
  "notes": "Approved by manager"
}
```

### Pay Commission
```http
POST /commissions/{id}/pay
Authorization: Bearer {token}
Content-Type: application/json

{
  "paymentReference": "PAY-2024-001",
  "notes": "Payment processed"
}
```

### Get Agent Commissions
```http
GET /commissions/by-agent/{agentId}?period=2024-01
Authorization: Bearer {token}
```

### Get Commission Statistics
```http
GET /commissions/statistics?period=2024-01
Authorization: Bearer {token}
```

## Products

### Get Products
```http
GET /products?page=0&size=20&status=ACTIVE
Authorization: Bearer {token}
```

### Get Product by ID
```http
GET /products/{id}
Authorization: Bearer {token}
```

### Create Product
```http
POST /products
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "Premium Insurance Policy",
  "productCode": "INS-PREM-001",
  "description": "Comprehensive insurance coverage",
  "category": "Insurance",
  "basePrice": 5000.00,
  "isCommissionEligible": true
}
```

### Update Product
```http
PUT /products/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "Updated Product Name",
  "basePrice": 5500.00,
  "status": "ACTIVE"
}
```

### Get Product Categories
```http
GET /products/categories
Authorization: Bearer {token}
```

### Get Commission Rules for Product
```http
GET /products/{id}/commission-rules
Authorization: Bearer {token}
```

## Dashboard

### Get Agent Dashboard
```http
GET /dashboard/agent?period=2024-01
Authorization: Bearer {token}
```

### Get Manager Dashboard
```http
GET /dashboard/manager?period=2024-01
Authorization: Bearer {token}
```

### Get Admin Dashboard
```http
GET /dashboard/admin?period=2024-01
Authorization: Bearer {token}
```

### Get Commission Trends
```http
GET /dashboard/commission-trends?period=last-6-months
Authorization: Bearer {token}
```

### Get Deal Status Breakdown
```http
GET /dashboard/deal-status-breakdown?period=2024-01
Authorization: Bearer {token}
```

## Reports

### Get Performance Report
```http
GET /reports/performance?agentId=1&period=2024-01
Authorization: Bearer {token}
```

### Get Commission Report
```http
GET /reports/commission?period=2024-01&status=PAID
Authorization: Bearer {token}
```

### Get Leaderboard
```http
GET /reports/leaderboard?period=2024-01&metric=commission
Authorization: Bearer {token}
```

### Export Report
```http
GET /reports/export?type=commission&format=excel&period=2024-01
Authorization: Bearer {token}
```

## Error Responses

### 400 Bad Request
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "status": 400,
  "error": "Bad Request",
  "message": "Validation failed",
  "path": "/api/deals",
  "errors": [
    {
      "field": "dealValue",
      "message": "Deal value must be greater than 0"
    }
  ]
}
```

### 401 Unauthorized
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "status": 401,
  "error": "Unauthorized",
  "message": "Invalid or expired token",
  "path": "/api/deals"
}
```

### 403 Forbidden
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "status": 403,
  "error": "Forbidden",
  "message": "Access denied",
  "path": "/api/users/1"
}
```

### 404 Not Found
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "status": 404,
  "error": "Not Found",
  "message": "Deal not found with ID: 999",
  "path": "/api/deals/999"
}
```

### 500 Internal Server Error
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "status": 500,
  "error": "Internal Server Error",
  "message": "An unexpected error occurred",
  "path": "/api/deals"
}
```

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **Authentication endpoints**: 5 requests per minute per IP
- **General endpoints**: 100 requests per minute per user
- **Export endpoints**: 10 requests per hour per user

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642248000
```

## Pagination

List endpoints support pagination with the following parameters:

- `page`: Page number (0-based, default: 0)
- `size`: Page size (default: 20, max: 100)
- `sort`: Sort field and direction (e.g., `createdAt,desc`)

Response includes pagination metadata:
```json
{
  "content": [...],
  "pageable": {
    "page": 0,
    "size": 20,
    "sort": "createdAt,desc"
  },
  "totalElements": 150,
  "totalPages": 8,
  "first": true,
  "last": false
}
```

## Filtering and Searching

Many endpoints support filtering and searching:

### Date Filtering
```http
GET /deals?dateFrom=2024-01-01&dateTo=2024-01-31
```

### Status Filtering
```http
GET /commissions?status=PAID,APPROVED
```

### Text Search
```http
GET /users/search?q=john&role=SALES_AGENT
```

### Range Filtering
```http
GET /deals?minValue=1000&maxValue=50000
```

## Webhooks (Future Feature)

The API will support webhooks for real-time notifications:

- Deal status changes
- Commission calculations
- Payment processing
- User activities

## SDK and Client Libraries

Official SDKs will be available for:

- JavaScript/TypeScript
- React Native
- Java
- Python

## Support

For API support and questions:
- Email: <EMAIL>
- Documentation: https://docs.quotasnap.com
- Status Page: https://status.quotasnap.com
